@extends('layouts.master')



@section('meta')
    <meta property="og:title" content="{{ $meta['title'] }}">
    <meta property="og:description" content="{{ $meta['desc'] }}">
@endsection



@section('header_css')
<style>
    .quick_view_slide .slick-dots {
        bottom: 0;
    }

    .star-rating i {
        font-size: 11px;
    }

    .all_rev {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80vw;
        height: 60vh;
        z-index: 999;
        padding: 20px 10px 20px 20px;
        opacity: 0;
        visibility: hidden;
        transition: .3s
    }

    .all_rev .reviews_info {
        height: 100%;
        overflow-y: scroll; 
    }

    .all_rev .close_link {
        position: absolute;
        right: 55px;
    }

    div#social-links {
        margin: 0;
        max-width: 500px;
        display: inline-block;
    }
    div#social-links ul {
        padding: 0;
    }
    div#social-links ul li {
        display: inline-block;
    }          
    div#social-links ul li a {
        padding: 6px 12px;
        /* border: 1px solid #ccc; */
        border-radius: 5px;
        margin: 5px;
        font-size: 18px;
        color: #636872;
        background-color: #f4f5f7;
    }

    /* Size Guide Modal Styles */
    .size-guide-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        overflow-y: auto;
    }

    .size-guide-content {
        position: relative;
        background-color: #fff;
        margin: 50px auto;
        padding: 0;
        width: 90%;
        max-width: 800px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        animation: modalFadeIn 0.3s ease-out;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .size-guide-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #ddd;
    }

    .size-guide-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #333;
        padding: 0;
        line-height: 1;
    }

    .size-guide-body {
        padding: 20px;
    }

    .size-guide-body table {
        width: 100%;
    }

    .size-guide-body th {
        background-color: #f5f5f5;
    }

    @media(min-width: 992px){
    
        .all_rev {
            width: 700px !important;
            padding: 30px 25px 30px 30px !important;
        }
            
    }

    @media(max-width: 767px){
        
        .single_rev_thumb {
            width: 45px;
        }
        .single_rev_thumb img {
            width: 100%;
        }

        .size-guide-content {
            width: 95%;
            margin: 30px auto;
        }

        .size-guide-body {
            padding: 15px;
        }
            
    }

    @media(max-width: 991px){

        .prd_details {
            margin-top: 20px !important;
        }
            
    }
</style>
@endsection

@section('content')
<!-- ======================= Top Breadcrubms ======================== -->
<div class="gray py-3">
    <div class="container">
        <div class="row">
            <div class="colxl-12 col-lg-12 col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{route('home_page')}}">
                            @if (session('lang_ben'))
                                হোম
                            @elseif (session('lang_fin'))
                                Etusivu
                            @else
                                Home
                            @endif
                        </a></li>
                        <li class="breadcrumb-item"><a href="{{route('shop_page')}}">
                            @if (session('lang_ben'))
                                দোকান
                            @elseif (session('lang_fin'))
                                Kauppa
                            @else
                                Shop
                            @endif
                        </a></li>
                        <li class="breadcrumb-item active" aria-current="page">
                            @if (session('lang_ben'))
                                পণ্য বিবরণ
                            @elseif (session('lang_fin'))
                                Tuotetiedot
                            @else
                                Product Details
                            @endif
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<!-- ======================= Top Breadcrubms ======================== -->

<!-- ======================= Product Detail ======================== -->
<section class="middle">
    <div class="container">

        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="sec_title position-relative text-center">
                    <h2 class="off_title">
                        @if (session('lang_ben'))
                            পণ্যের বিবরণ
                        @elseif (session('lang_fin'))
                            Tuotekuvaus
                        @else
                            Item Description
                        @endif
                    </h2>
                    <h3 class="ft-bold pt-3">
                        @if (session('lang_ben'))
                            পণ্য বিবরণ
                        @elseif (session('lang_fin'))
                            Tuotetiedot
                        @else
                            Product Details
                        @endif
                    </h3>
                </div>
            </div>
        </div>
        
        <div class="row justify-content-between">

            {{-- === Thumbnail Slider === --}}
            <div class="col-xl-5 col-lg-6 col-md-12 col-sm-12">
                <div class="quick_view_slide" style="border: 1px solid rgba(128, 134, 134, 0.527)">
                    @foreach ($thumbnail as $thumb)
                        <div class="single_view_slide"><a href="" data-lightbox="roadtrip" class="d-block mb-4"><img src="{{asset('uploads/product/thumbnails')}}/{{$thumb->thumbnail}}" class="img-fluid rounded" style="object-fit: scale-down" alt="Product Thumbnails"></a></div>
                    @endforeach
                </div>
            </div>

            <div class="col-xl-7 col-lg-6 col-md-12 col-sm-12">
                <div class="prd_details pl-3">
                    
                    <div class="prt_01 mb-1"><span class="text-light bg-info rounded px-2 py-1">
                        @if (session('lang_fin'))
                            {{ $product_info->relto_cata->cata_name_fin ?? $product_info->relto_cata->cata_name }}
                        @elseif (session('lang_ben'))
                            {{ $product_info->relto_cata->cata_name_ben ?? $product_info->relto_cata->cata_name }}
                        @else
                            {{ $product_info->relto_cata->cata_name }}
                        @endif
                    </span></div>
                    <div class="prt_02 mb-3">
                        <h2 class="ft-bold mb-1">
                            @if (session('lang_fin') && $product_info->product_name_fi)
                                {{$product_info->product_name_fi}}
                            @else
                                {{$product_info->product_name}}
                            @endif
                        </h2>
                        <div class="text-left">
                            @php
                                $avg_star = \App\Models\OrdereditemsTab::where('product_id', $product_info->id)->avg('star');
                            @endphp

                            <div class="star-rating align-items-center d-flex justify-content-left mb-1 p-0">
                                @for ($i = 1; $i <= $avg_star; $i ++)
                                    <i class="fas fa-star filled"></i>

                                    @if ($avg_star - $i < 1 && $avg_star - $i > 0)
                                        <i class="fad fa-star-half" style="--fa-secondary-opacity: 1.0; --fa-primary-color: #FF9800; --fa-secondary-color: #D6DDE6;"></i>
                                    @endif
                                @endfor

                                @for ($i = 1; $i <= 5-$avg_star; $i ++)
                                    <i class="fas fa-star"></i>
                                @endfor
                                
                                @php
                                    $review_count = \App\Models\OrdereditemsTab::where('product_id', $product_info->id)->whereNotNull('review')->count();
                                @endphp
                                <span class="small">({{$review_count}} Reviews)</span>
                            </div>
                            @if ($product_info->discount != 0)
                                <span class="ft-medium text-muted line-through fs-md mr-2">{{$product_info->price}}</span>
                                <span class="ft-bold theme-cl fs-lg mr-2">{{number_format($product_info->after_disc)}}&#8364;</span>
                            @else
                                <span class="ft-bold theme-cl fs-lg mr-2">{{number_format($product_info->price)}}&#8364;</span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="prt_03 mb-4">
                        <p>
                            @if (session('lang_fin') && $product_info->short_desc_fi)
                                {{$product_info->short_desc_fi}}
                            @else
                                {{$product_info->short_desc}}
                            @endif
                        </p>
                    </div>
                    
                    <form action="{{route('cart.store')}}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <input type="text" name="product_id" hidden value="{{$product_info->id}}">

                        {{-- === Product Colors === --}}
                        <div class="prt_04 mb-2">
                            <p class="d-flex align-items-center mb-0 text-dark ft-medium">
                                @if (session('lang_ben'))
                                    রঙ:
                                @elseif (session('lang_fin'))
                                    Väri:
                                @else
                                    Color:
                                @endif
                            </p>
                            <div class="text-left">
                                @foreach ($color_info as $color)

                                    @if ($color->color != 1)
                                        <div class="form-check form-option form-check-inline mb-1">
                                            <input {{$color->color == old('prod_color') ?'checked' :''}}
                                            class="form-check-input color_sec" type="radio" name="prod_color" id="col{{$color->color}}" 
                                            value="{{old('prod_color') != null ?old('prod_color') :$color->color}}">
                                            {{-- {{$color->color}} --}}
                                            <label class="form-option-label rounded-circle" for="col{{$color->color}}"><span class="form-option-color rounded-circle" style="background: {{$color->relto_color->color_code}}"></span></label>
                                            {{-- {{old('prod_color')}} --}}
                                        </div>
                                    @else
                                        <div class="form-check size-option form-option form-check-inline mb-2">
                                            <input class="form-check-input color_sec" type="radio" name="prod_color" value="{{$color->color}}" id="col{{$color->color}}" checked>
                                            <label class="form-option-label" for="col{{$color->color}}">N/A</label>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                            @error('prod_color')
                                <strong class="text-danger">{{$message}}</strong>
                            @enderror
                        </div>
                        
                        {{-- === Product Size === --}}
                        <div class="prt_04 mb-4">
                            <p class="d-flex align-items-center mb-0 text-dark ft-medium">
                                @if (session('lang_ben'))
                                    আকার:
                                @elseif (session('lang_fin'))
                                    Koko:
                                @else
                                    Size:
                                @endif
                                <a href="javascript:void(0)" class="btn-sm ml-2 bg-light text-dark ft-medium" id="sizeGuideBtn">
                                    <i class="lni lni-ruler"></i> Size Guide
                                </a>
                            </p>
                            <div class="text-left pb-0 pt-2 size_sec">
                                @foreach ($size_info as $size)
                                    @if ($size->size != 17)
                                        <div class="form-check size-option form-option form-check-inline mb-2 sp_size_div">
                                            @php
                                                $new_size = \App\Models\Inventory::where('product_id', $product_info->id)->where('color', old('prod_color'))->where('size', $size->size)->get();
                                            @endphp
                                            
                                            @if (old('prod_color'))
                                                @foreach ($new_size as $newz)
                                                    @if ($newz != null)
                                                        <input {{$newz->size == old('prod_size') ?'checked' :''}}
                                                         class="form-check-input size_inp" type="radio" 
                                                        value="{{$size->size}}" 
                                                        name="prod_size" id="siz{{$size->size}}">
                                                        {{-- {{$size->size}} --}}
                                                        <label class="form-option-label" for="siz{{$size->size}}">{{$size->relto_size->size}}</label>
                                                    @endif
                                                @endforeach
                                            @else
                                                <input class="form-check-input" type="radio" 
                                                value="{{$size->size}}" 
                                                name="prod_size" id="siz{{$size->size}}">
                                                {{-- {{$size->size}} --}}
                                                <label class="form-option-label" for="siz{{$size->size}}">{{$size->relto_size->size}}</label>
                                            @endif
                                            
                                            {{-- @foreach ($new_size as $newz)
                                                @if ($newz != null)
                                                    {{$newz->size}}
                                                @endif
                                            @endforeach --}}
                                        </div>
                                    @else
                                        <div class="form-check size-option form-option form-check-inline mb-2">
                                            <input class="form-check-input" type="radio" name="prod_size" value="{{$size->size}}" id="siz{{$size->size}}" checked>
                                            <label class="form-option-label" for="siz{{$size->size}}">{{$size->relto_size->size}}</label>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                            @error('prod_size')
                                <strong class="text-danger">{{$message}}</strong>
                            @enderror
                        </div>

                        {{-- === Custom Item Type Field for Stickers === --}}
                        @php
                            $isSticker = false;
                            $isPictureSticker = false;
                            $stickerSubcategories = \App\Models\Subcategory::where('sub_cata_name', 'LIKE', '%sticker%')->get();
                            foreach($stickerSubcategories as $subcategory) {
                                if($product_info->subcata_id == $subcategory->id) {
                                    $isSticker = true;
                                    if(stripos($subcategory->sub_cata_name, 'picture') !== false) {
                                        $isPictureSticker = true;
                                    }
                                    break;
                                }
                            }
                        @endphp

                        @if($isSticker)
                        <div class="prt_04 mb-4">
                            <p class="d-flex align-items-center mb-0 text-dark ft-medium">
                                @if (session('lang_ben'))
                                    আইটেম টাইপ:
                                @elseif (session('lang_fin'))
                                    Kohteen tyyppi:
                                @else
                                    Item Type:
                                @endif
                            </p>
                            <div class="text-left pb-0 pt-2">
                                <input type="text"
                                       name="item_type"
                                       class="form-control"
                                       placeholder="@if (session('lang_ben'))
ডিভাইস মডেল, গাড়ি, মগ ইত্যাদি
                                       @elseif (session('lang_fin'))
Laitemalli, auto, muki jne.
                                       @else 
Device Models, Car, Mug etc.
                                       @endif
                                       "
                                       value="{{ old('item_type') }}">
                            </div>
                            @error('item_type')
                                <strong class="text-danger">{{$message}}</strong>
                            @enderror
                        </div>
                        @endif

                        {{-- === Picture Upload Field for PictureSticker === --}}
                        @if($isPictureSticker)
                        <div class="prt_04 mb-4">
                            <p class="d-flex align-items-center mb-0 text-dark ft-medium">
                                @if (session('lang_ben'))
                                    আপনার ছবি আপলোড করুন:
                                @elseif (session('lang_fin'))
                                    Lataa kuvasi:
                                @else
                                    Upload Your Picture:
                                @endif
                            </p>
                            <div class="text-left pb-0 pt-2">
                                <input type="file"
                                       name="customer_picture"
                                       class="form-control"
                                       accept="image/*"
                                       id="customer_picture_upload">
                                <small class="form-text text-muted">
                                    @if (session('lang_ben'))
                                        সমর্থিত ফরম্যাট: JPG, PNG, GIF (সর্বোচ্চ 5MB)
                                    @elseif (session('lang_fin'))
                                        Tuetut muodot: JPG, PNG, GIF (enintään 5MB)
                                    @else
                                        Supported formats: JPG, PNG, GIF (Max 5MB)
                                    @endif
                                </small>
                            </div>
                            @error('customer_picture')
                                <strong class="text-danger">{{$message}}</strong>
                            @enderror
                        </div>
                        @endif

                        {{-- <input type="hidden" name="qty_output" id="quantity_op"> --}}

                        <div class="prt_05 mb-4">
                            <div class="form-row mb-7">

                                <!-- Quantity -->
                                <div class="col-12 col-sm-2 col-md-2 col-lg-auto">
                                    @php
                                        $sizeless = \App\Models\Inventory::where('product_id', $product_info->id)->where('color', 1)->where('size', 17)->first();
                                    @endphp

                                    @if ($sizeless != '')
                                        <select class="mb-2 custom-select qty_sec" name="quantity">
                                            @for ($i=1; $i <= $sizeless->quantity; $i++)
                                                <option value="{{$i}}">{{$i}}</option>
                                            @endfor
                                        </select>
                                    @else
                                        <select class="mb-2 custom-select qty_sec" name="quantity">
                                            <option value=""></option>
                                        </select>
                                    @endif
                                </div>

                                <!-- Submit -->
                                <div class="col-12 col-sm-5 col-md-5 col-lg">
                                    <button type="submit" class="btn btn-block custom-height bg-dark mb-2">
                                        <i class="lni lni-shopping-basket mr-2"></i>
                                        @if (session('lang_fin'))
                                            Lisää koriin
                                        @elseif (session('lang_ben'))
                                            কার্টে যোগ করুন
                                        @else
                                            Add to Cart
                                        @endif
                                    </button>
                                </div>

                                {{-- === Wish Button === --}}
                                @php
                                    $wish_exists = \App\Models\WishTable::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $product_info->id)->exists();
                                @endphp
                                @if($wish_exists)
                                    <div class="col-12 col-sm-5 col-md-5 col-lg-auto">

                                        <!-- Remove Wishlist -->
                                        <a class="btn custom-height btn-block mb-2 text-white" 
                                        href="{{route('wishlist.remove.btn', $product_info->id)}}" 
                                        style="background: green !important" id="rem_wish">
                                            <i class="lni lni-heart mr-2"></i>
                                            @if (session('lang_fin'))
                                                Poista wishlistista
                                            @elseif (session('lang_ben'))
                                                প্রিয় তালিকার থেকে সরান
                                            @else
                                                Remove from Wishlist
                                            @endif
                                        </a>
                                    </div>
                                @else
                                    <div class="col-12 col-sm-5 col-md-5 col-lg-auto">
                                        
                                        <!-- Wishlist -->
                                        <button class="btn custom-height btn-default btn-block mb-2 text-dark" 
                                        formaction="{{route('wishlist.store')}}" 
                                        style="border: 1px solid black" name="wish_btn" id="add_wish">
                                            <i class="lni lni-heart mr-2"></i>
                                            @if (session('lang_fin'))
                                                Lisää wishlistiin
                                            @elseif (session('lang_ben'))
                                                প্রিয় তালিকাযোগ করুন
                                            @else
                                                Add to Wishlist
                                            @endif
                                        </button>
                                    </div>
                                @endif
                            </div>
                            @error('quantity')
                                <strong class="text-danger" style="font-size: 18px">{{$message}}</strong>
                            @enderror
                        </div>
                    </form>
                    
                    <div class="prt_06">
                        <p class="mb-0 d-inline-block">
                          <span class="mr-4">
                            @if (session('lang_fin'))
                                Jaa:
                            @elseif (session('lang_ben'))
                                শেয়ার করুন:
                            @else
                                Share:
                            @endif
                          </span>
                          {!! $shareComponent !!}
                        </p>
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>
<!-- ======================= Product Detail End ======================== -->

<!-- ======================= Product Description ======================= -->
<section class="middle">
    <div class="container">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-11 col-lg-12 col-md-12 col-sm-12">
                <ul class="nav nav-tabs b-0 d-flex align-items-center justify-content-center simple_tab_links mb-4" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link active" id="description-tab" href="#description" data-toggle="tab" role="tab" aria-controls="description" aria-selected="true">
                            @if (session('lang_fin'))
                            Tuotekuvaus
                            @elseif (session('lang_ben'))
                                বিবরণ
                            @else
                                Description
                            @endif
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" href="#information" id="information-tab" data-toggle="tab" role="tab" aria-controls="information" aria-selected="false">
                            @if (session('lang_fin'))
                            Lisätiedot
                            @elseif (session('lang_ben'))
                                বিবরণ
                            @else
                                Additional information
                            @endif
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" href="#reviews" id="reviews-tab" data-toggle="tab" role="tab" aria-controls="reviews" aria-selected="false">
                            @if (session('lang_fin'))
                            Arvostelut
                            @elseif (session('lang_ben'))
                                রিভিউ
                            @else
                                Reviews
                            @endif
                        </a>
                    </li>
                </ul>
                
                <div class="tab-content" id="myTabContent">
                    
                    <!-- Description Content -->
                    <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                        <div class="description_info">
                            <p class="p-0 mb-2">
                                @if (session('lang_fin') && $product_info->long_desc_fi)
                                    {!! $product_info->long_desc_fi !!}
                                @else
                                    {!! $product_info->long_desc !!}
                                @endif
                            </p>
                        </div>
                    </div>
                    
                    <!-- Additional Content -->
                    <div class="tab-pane fade" id="information" role="tabpanel" aria-labelledby="information-tab">
                        <div class="additionals">
                            <table class="table">
                                <tbody>
                                    <tr>
                                      <th class="ft-medium text-dark">ID</th>
                                      <td>#1253458</td>
                                    </tr>
                                    <tr>
                                      <th class="ft-medium text-dark">SKU</th>
                                      <td>KUM125896</td>
                                    </tr>
                                    <tr>
                                      <th class="ft-medium text-dark">Color</th>
                                      <td>Sky Blue</td>
                                    </tr>
                                    <tr>
                                      <th class="ft-medium text-dark">Size</th>
                                      <td>Xl, 42</td>
                                    </tr>
                                    <tr>
                                      <th class="ft-medium text-dark">Weight</th>
                                      <td>450 Gr</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Reviews Content -->
                    <div class="tab-pane fade {{session('rev_error') ?'show active' :''}}" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                        <div class="reviews_info">

                            @php
                                $reviews_limited = \App\Models\OrdereditemsTab::where('product_id', $product_info->id)->whereNotNull('review')->orderBy('star', 'desc')->take(3)->get();
                            @endphp
                            @forelse ($reviews_limited as $review)
                                <div class="single_rev d-flex align-items-start br-bottom py-3">
                                    <div class="single_rev_thumb">
                                        @if ($review->relto_cust && $review->relto_cust->prof_pic)
                                            <img src="{{ asset('uploads/customer') }}/{{ $review->relto_cust->prof_pic }}" class="img-fluid circle" width="90" alt="Customer" />
                                        @else
                                            <img src="{{ asset('uploads/customer/default.png') }}" class="img-fluid circle" width="90" alt="Customer" />
                                        @endif
                                    </div>
                                    <div class="single_rev_caption d-flex align-items-start pl-3">
                                        <div class="single_capt_left">
                                            <h5 class="fs-md ft-medium lh-1">
                                                {{ $review->relto_cust ? $review->relto_cust->name : 'Unknown Customer' }}
                                            </h5>
                                            <div class="single_capt_right">
                                                <div class="star-rating align-items-center d-flex justify-content-left mb-1 p-0">
                                                    @for ($i = 1; $i <= $review->star; $i ++)
                                                        <i class="fas fa-star filled"></i>
                                                    @endfor
                                                    @for ($i = 1; $i <= 5-$review->star; $i ++)
                                                        <i class="fas fa-star"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <span class="small">{{$review->updated_at->isoFormat('DD-MMM-YY')}}</span>
                                            <p style="line-height: 18px; margin: 10px 0;">{{$review->review}}</p>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="item py-5">
                                    <h6>** No Reviews Yet !!</h6>
                                </div>
                            @endforelse

                            @php
                                $total_reviews = \App\Models\OrdereditemsTab::where('product_id', $product_info->id)->whereNotNull('review')->count();
                            @endphp
                            @if ($total_reviews > 3)
                                <div class="item" style="margin: 15px;">
                                    <button class="btn btn-primary rev_btn">
                                        @if (session('lang_fin'))
                                            Näytä kaikki
                                        @elseif (session('lang_ben'))
                                            সব দেখুন
                                        @else
                                            View All
                                        @endif
                                    </button>
                                </div>
                            @endif
                        </div>
                        
                        {{-- === Submit Review === --}}
                        @php
                            $ordered_product = \App\Models\OrdereditemsTab::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $product_info->id);
                        @endphp

                        @auth('cust_login')
                            @if ($ordered_product->doesntExist())
                                <div class="alert alert-info d-flex" style="justify-content: space-between; align-items:center">
                                    <span style="font-size: 16px">
                                        @if (session('lang_fin'))
                                            Osta tuote jotta voit jättää arvostelun!
                                        @elseif (session('lang_ben'))
                                            প্রিয় তালিকাযোগ করুন
                                        @else
                                            Please Purchase the item to leave a Review!
                                        @endif
                                    </span> 
                                    <a class="btn btn-primary" href="#">Go to Top</a>
                                </div>
                            @elseif ($ordered_product->whereNotNull('review')->first())
                                <div class="alert alert-success d-flex" style="justify-content: space-between; align-items:center">
                                    <span style="font-size: 16px">
                                        @if (session('lang_fin'))Olet jo jättänyt arvostelun!
                                        @elseif (session('lang_ben'))প্রিয় তালিকাযোগ করুন
                                        @else
                                        You have already left a Review!
                                        @endif
                                    

                                    </span> 
                                    <a class="btn btn-primary" href="#">Go to Top</a>
                                </div>
                            @else
                                <div class="reviews_rate mt-3 {{session('rev_error') ?'err' :''}}">
                                    <form class="row" action="{{route('product.review', $product_info->id)}}" method="POST">
                                        @csrf

                                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                                            <h4>
                                                @if (session('lang_fin'))
                                                    Jätä arvostelu
                                                @elseif (session('lang_ben'))
                                                    রিভিউ দিন
                                                @else
                                                    Submit Your Rating
                                                @endif

                                            </h4>
                                        </div>
                                        
                                        {{-- === Star === --}}
                                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                                            <div class="revie_stars d-flex align-items-center justify-content-between px-2 py-2 gray rounded mb-2 mt-1">
                                                <div class="srt_013">
                                                    <div class="submit-rating">
                                                    <input class="star_cls" id="star-5" type="radio" name="rating" value="5" />
                                                    <label for="star-5" title="5 stars">
                                                        <i class="active fa fa-star" aria-hidden="true"></i>
                                                    </label>
                                                    <input class="star_cls" id="star-4" type="radio" name="rating" value="4" />
                                                    <label for="star-4" title="4 stars">
                                                        <i class="active fa fa-star" aria-hidden="true"></i>
                                                    </label>
                                                    <input class="star_cls" id="star-3" type="radio" name="rating" value="3" />
                                                    <label for="star-3" title="3 stars">
                                                        <i class="active fa fa-star" aria-hidden="true"></i>
                                                    </label>
                                                    <input class="star_cls" id="star-2" type="radio" name="rating" value="2" />
                                                    <label for="star-2" title="2 stars">
                                                        <i class="active fa fa-star" aria-hidden="true"></i>
                                                    </label>
                                                    <input class="star_cls" id="star-1" type="radio" name="rating" value="1" />
                                                    <label for="star-1" title="1 star">
                                                        <i class="active fa fa-star" aria-hidden="true"></i>
                                                    </label>
                                                    </div>
                                                </div>
                                                
                                                <div class="srt_014">
                                                    <h6 class="mb-0"><span id="star_show">
                                                        @if (session('lang_fin'))
                                                            Napsauta
                                                        @elseif (session('lang_ben'))
                                                            টিপুন
                                                        @else
                                                            Click on
                                                        @endif
                                                    </span> 
                                                    @if (session('lang_fin'))
                                                        Tähtiä
                                                    @elseif (session('lang_ben'))
                                                        টিপুন
                                                    @else
                                                        Star!
                                                    @endif
                                                    
                                                   </h6>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        {{-- === Name/Email === --}}
                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                            <div class="form-group">
                                                <label class="medium text-dark ft-medium">
                                                    @if (session('lang_fin'))
                                                        Nimesi
                                                    @elseif (session('lang_ben'))
                                                        আপনার নাম
                                                    @else
                                                        Your Name
                                                    @endif
                                                
                                               </label>
                                                <input value="{{Auth::guard('cust_login')->user()->name}}" type="text" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                            <div class="form-group">
                                                <label class="medium text-dark ft-medium">
                                                    @if (session('lang_fin'))
                                                        Sähköpostiosoite
                                                    @elseif (session('lang_ben'))
                                                        আপনার ইমেইল ঠিকানা
                                                    @else
                                                        Email Address
                                                    @endif
                                                </label>
                                                <input value="{{Auth::guard('cust_login')->user()->email}}" type="email" class="form-control" />
                                            </div>
                                        </div>
                                        
                                        {{-- === Description === --}}
                                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                                            <div class="form-group">
                                                <label class="medium text-dark ft-medium">
                                                    @if (session('lang_fin'))
                                                        Kuvaus
                                                    @elseif (session('lang_ben'))
                                                        বিবরণ
                                                    @else
                                                        Description
                                                    @endif
                                                
                                                </label>
                                                <textarea name="review" class="form-control">{{ old('review') }}</textarea>
                                            </div>
                                        </div>
                                        
                                        {{-- === Submit === --}}
                                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                                            <div class="form-group m-0">
                                                <button class="btn btn-white stretched-link hover-black">
                                                    @if (session('lang_fin'))
                                                        Jätä arvostelu
                                                    @elseif (session('lang_ben'))
                                                        রিভিউ দিন
                                                    @else
                                                        Submit Review
                                                    @endif
                                                 <i class="lni lni-arrow-right"></i></button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            @endif
                        @else
                            <div class="alert alert-warning d-flex" style="justify-content: space-between; align-items:center">
                                <span style="font-size: 16px">
                                    @if (session('lang_fin'))
                                        Kirjaudu sisään jotta voit jättää arvostelun!
                                    @elseif (session('lang_ben'))
                                        রিভিউ দিন
                                    @else
                                        Please Login to leave a Review!
                                    @endif
                                
                                </span> 
                                <a class="btn btn-primary" href="{{route('customer_login')}}">
                                    @if (session('lang_fin'))
                                        Kirjaudu sisään
                                    @elseif (session('lang_ben'))
                                        রিভিউ দিন
                                    @else
                                        Login Here
                                    @endif
                                
                                </a>
                            </div>
                        @endauth
                    </div>
                </div>
            </div>
        </div>

        {{-- === Show All Reviews === --}}
        <div class="all_rev" style="background: #fffefb;">
            <div class="close_link">
                <button class="btn btn-primary close_btn rev_close_btn">
                    @if (session('lang_fin'))
                        Sulje
                    @elseif (session('lang_ben'))
                        বন্ধ
                    @else
                        Close
                    @endif
                </button>
            </div>

            <div class="reviews_info">
                @php
                    $all_reviews = \App\Models\OrdereditemsTab::where('product_id', $product_info->id)->whereNotNull('review')->orderBy('star', 'desc')->get();
                @endphp
                @foreach ($all_reviews as $review)
                    <div class="single_rev d-flex align-items-start br-bottom py-3">
                        <div class="single_rev_thumb">
                            @if ($review->relto_cust && $review->relto_cust->prof_pic)
                                <img src="{{ asset('uploads/customer') }}/{{ $review->relto_cust->prof_pic }}" class="img-fluid circle" width="90" alt="Customer" />
                            @else
                                <img src="{{ asset('uploads/customer/default.png') }}" class="img-fluid circle" width="90" alt="Customer" />
                            @endif
                        </div>

                        <div class="single_rev_caption d-flex align-items-start pl-3">
                            <div class="single_capt_left">
                                <h5 class="fs-md ft-medium lh-1">
                                    {{ $review->relto_cust ? $review->relto_cust->name : 'Unknown Customer' }}
                                </h5>
                                <div class="single_capt_right">
                                    <div class="star-rating align-items-center d-flex justify-content-left mb-1 p-0">
                                        @for ($i = 1; $i <= $review->star; $i ++)
                                            <i class="fas fa-star filled"></i>
                                        @endfor
                                        @for ($i = 1; $i <= 5-$review->star; $i ++)
                                            <i class="fas fa-star"></i>
                                        @endfor
                                    </div>
                                </div>
                                <span class="small">{{$review->updated_at->isoFormat('DD-MMM-YY')}}</span>
                                <p style="line-height: 18px; margin: 10px 0;">{{$review->review}}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</section>
<!-- ======================= Product Description End ==================== -->

<!-- ======================= Similar Products Start ============================ -->
<section class="middle pt-0">
    <div class="container">
        
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="sec_title position-relative text-center">
                    <h2 class="off_title">
                        @if (session('lang_fin'))
                            Samanlaiset tuotteet
                        @elseif (session('lang_ben'))
                            সমান পণ্য
                        @else
                            Similar Products
                        @endif
                    
                    </h2>
                    <h3 class="ft-bold pt-3">
                        @if (session('lang_fin'))
                            Vastaavat tuotteet
                        @elseif (session('lang_ben'))
                            সমান পণ্য
                        @else
                            Matching Products
                        @endif
                    
                    
                    </h3>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="slide_items">
                    
                    <!-- single Item -->
                    @foreach ($rel_products as $rel)
                        <div class="single_itesm">
                            <div class="product_grid card mb-0">
                                @if ($rel->discount != 0)
                                    <div class="badge bg-success text-white position-absolute ft-regular ab-left text-upper">Sale</div>
                                    <div class="badge bg-danger text-white position-absolute ft-regular ab-right text-upper">-{{$rel->discount}}%</div>
                                @endif

                                @if(Carbon\carbon::now()->diffInDays($rel->updated_at) < 7)
                                    <div class="badge text-white position-absolute ft-regular ab-right text-upper" style="top: 68%; background: rgba(0, 0, 0, 0.5); border: 1px solid whitesmoke">
                                        @if (session('lang_fin'))
                                            Uusi tulevaisuus!
                                        @elseif (session('lang_ben'))
                                            নতুন প্রতিষ্ঠান!
                                        @else
                                            New Arrival!
                                        @endif
                                    
                                    </div>
                                @endif

                                <div class="card-body p-0">
                                    <div class="shop_thumb position-relative">
                                        <a class="card-img-top d-block overflow-hidden" href="{{route('product.details', $rel->slug)}}"><img class="card-img-top" style="object-fit: scale-down" src="{{asset('uploads/product/preview')}}/{{$rel->preview}}" alt="Product Preview"></a>
                                    </div>
                                </div>
                                <div class="card-footer p-3 pb-0 d-flex align-items-start justify-content-center">
                                    <div class="text-left">
                                        <div class="text-center">
                                            <h5 class="fw-bolder fs-md mb-0 lh-1 mb-1"><a href="shop-single-v1.html">{{$rel->product_name}}</a></h5>
                                            @if ($rel->discount != 0)
                                                <span class="ft-medium text-muted line-through fs-md mr-2">{{$rel->price}}</span>
                                                <span class="ft-bold theme-cl fs-md mr-2">{{number_format($rel->after_disc)}}&#8364;</span>
                                            @else
                                                <span class="ft-bold theme-cl fs-md mr-2">{{number_format($rel->price)}}&#8364;</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        
    </div>
</section>
<!-- ======================= Similar Products Start ============================ -->

<!-- Size Guide Modal -->
<div class="size-guide-modal" id="sizeGuideModal">
    <div class="size-guide-content">
        <div class="size-guide-header">
            <h4>
                @if (session('lang_ben'))
                    আকার গাইড
                @elseif (session('lang_fin'))
                    Kokotaulukko
                @else
                    Size Chart
                @endif
                - {{ $product_info->relto_cata->cata_name }}
            </h4>
            <button class="close-btn" id="closeModal">&times;</button>
        </div>
        <div class="size-guide-body">
            @php
                // Get the category ID to determine which size chart to show
                $category_id = $product_info->relto_cata->id;
            @endphp
            
            @if($category_id == 1 || $category_id == 4) {{-- Clothing / Apparel Categories --}}
                <table class="table table-bordered">
                    <thead class="thead-dark">
                        <tr>
                            <th>Size</th>
                            <th>Chest (inches)</th>
                            <th>Waist (inches)</th>
                            <th>Hips (inches)</th>
                            <th>Length (inches)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>S</td>
                            <td>36-38</td>
                            <td>28-30</td>
                            <td>35-37</td>
                            <td>27-28</td>
                        </tr>
                        <tr>
                            <td>M</td>
                            <td>39-41</td>
                            <td>31-33</td>
                            <td>38-40</td>
                            <td>28-29</td>
                        </tr>
                        <tr>
                            <td>L</td>
                            <td>42-44</td>
                            <td>34-36</td>
                            <td>41-43</td>
                            <td>29-30</td>
                        </tr>
                        <tr>
                            <td>XL</td>
                            <td>45-47</td>
                            <td>37-39</td>
                            <td>44-46</td>
                            <td>30-31</td>
                        </tr>
                        <tr>
                            <td>XXL</td>
                            <td>48-50</td>
                            <td>40-42</td>
                            <td>47-49</td>
                            <td>31-32</td>
                        </tr>
                    </tbody>
                </table>
            @elseif($category_id == 3) {{-- Footwear Category --}}
                <table class="table table-bordered">
                    <thead class="thead-dark">
                        <tr>
                            <th>EU Size</th>
                            <th>US Size</th>
                            <th>UK Size</th>
                            <th>Foot Length (cm)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>38</td>
                            <td>5.5</td>
                            <td>5</td>
                            <td>24</td>
                        </tr>
                        <tr>
                            <td>39</td>
                            <td>6.5</td>
                            <td>6</td>
                            <td>24.6</td>
                        </tr>
                        <tr>
                            <td>40</td>
                            <td>7</td>
                            <td>6.5</td>
                            <td>25.4</td>
                        </tr>
                        <tr>
                            <td>41</td>
                            <td>8</td>
                            <td>7.5</td>
                            <td>26</td>
                        </tr>
                        <tr>
                            <td>42</td>
                            <td>8.5</td>
                            <td>8</td>
                            <td>26.7</td>
                        </tr>
                        <tr>
                            <td>43</td>
                            <td>9.5</td>
                            <td>9</td>
                            <td>27.3</td>
                        </tr>
                        <tr>
                            <td>44</td>
                            <td>10</td>
                            <td>9.5</td>
                            <td>28</td>
                        </tr>
                        <tr>
                            <td>45</td>
                            <td>11</td>
                            <td>10.5</td>
                            <td>28.6</td>
                        </tr>
                    </tbody>
                </table>
                @elseif($category_id == 2) {{-- Electronics Category --}}
                <table class="table table-bordered">
                    <thead class="thead-dark">
                        <tr>
                            <th>Specification</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Power Input</td>
                            <td>100-240V AC, 50/60Hz</td>
                        </tr>
                        <tr>
                            <td>Power Consumption</td>
                            <td>Up to 65W</td>
                        </tr>
                        <tr>
                            <td>Operating Temperature</td>
                            <td>0°C to 40°C</td>
                        </tr>
                        <tr>
                            <td>Storage Temperature</td>
                            <td>-20°C to 60°C</td>
                        </tr>
                        <tr>
                            <td>Humidity</td>
                            <td>5% to 95% non-condensing</td>
                        </tr>
                        <tr>
                            <td>Warranty Period</td>
                            <td>12 months from date of purchase</td>
                        </tr>
                        <tr>
                            <td>Certification</td>
                            <td>CE, FCC, RoHS compliant</td>
                        </tr>
                    </tbody>
                </table>
            @else {{-- Default Size Chart for Other Categories --}}
                <table class="table table-bordered">
                    <thead class="thead-dark">
                        <tr>
                            <th>Size</th>
                            <th>Measurement (cm)</th>
                            <th>Recommended For</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Small</td>
                            <td>50-60</td>
                            <td>Extra Small Build</td>
                        </tr>
                        <tr>
                            <td>Medium</td>
                            <td>60-70</td>
                            <td>Average Build</td>
                        </tr>
                        <tr>
                            <td>Large</td>
                            <td>70-80</td>
                            <td>Larger Build</td>
                        </tr>
                        <tr>
                            <td>XL</td>
                            <td>80-90</td>
                            <td>Extra Large Build</td>
                        </tr>
                    </tbody>
                </table>
            @endif
            
            <p class="mt-3 text-muted small">
                @if (session('lang_ben'))
                    নোট: আকার চার্ট আনুমানিক। প্রকৃত আকার স্টাইল এবং ডিজাইন অনুসারে সামান্য পরিবর্তিত হতে পারে।
                @elseif (session('lang_fin'))
                    Huomautus: Kokotaulukko on likimääräinen. Todelliset koot voivat vaihdella hieman tyylin ja muotoilun mukaan.
                @else
                    Note: Size chart is approximate. Actual sizes may vary slightly by style and design.
                @endif
            </p>
            
            <div class="mt-4">
                <h6 class="font-weight-bold">
                    @if (session('lang_ben'))
                        আপনার আকার নিতে কিভাবে
                    @elseif (session('lang_fin'))
                        Kuinka ottaa mittasi
                    @else
                        How to Take Your Measurements
                    @endif
                </h6>
                <ul class="text-muted">
                    <li>
                        @if (session('lang_ben'))
                            বুক: সবচেয়ে চওড়া অংশে বুকের চারপাশে মাপুন।
                        @elseif (session('lang_fin'))
                            Rinta: Mittaa rinnan ympärys leveimmältä kohdalta.
                        @else
                            Chest: Measure around the chest at the widest part.
                        @endif
                    </li>
                    <li>
                        @if (session('lang_ben'))
                            কোমর: কোমরের সবচেয়ে সরু অংশে মাপুন।
                        @elseif (session('lang_fin'))
                            Vyötärö: Mittaa vyötärön ympärys kapeimmalta kohdalta.
                        @else
                            Waist: Measure around the narrowest part of your waist.
                        @endif
                    </li>
                    <li>
                        @if (session('lang_ben'))
                            হিপস: আপনার হিপসের সবচেয়ে চওড়া অংশের চারপাশে মাপুন।
                        @elseif (session('lang_fin'))
                            Lantio: Mittaa lantion ympärys leveimmältä kohdalta.
                        @else
                            Hips: Measure around the widest part of your hips.
                        @endif
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@endsection

@section('footer_script')

{{-- === Get Size Ajax === --}}
<script>
    $('.color_sec').click(function(){
        var product_id = "{{$product_info->id}}";
        var color_id = $(this).val();

		// Root Ajax setup code
		$.ajaxSetup({
			headers: {
				'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
			}
		});
		
		// Custom Ajax to send 'cata_id' to Route & controller
		$.ajax ({
			url: '/get_size',
			type: 'POST',
			data: {'product_id': product_id, 'color_id': color_id}, 
			
			//Data Receive from controller
			success: function(data){
				$('.size_sec').html(data);
			}
		})
    })
</script>

{{-- === Remove Empty Size-Div === --}}
@if (old('prod_color'))
<script>
    $(document).ready(function(){
        $(".sp_size_div").not(":has(input)").css("display", "none");
    });
</script>
@endif

{{-- === Quantity Click === --}}
<script>
    $('.size_sec').click(function(){
        var product_id = "{{$product_info->id}}";
        var color_id = $('.color_sec:checked').val();
        var size_id = $('.size_inp:checked').val();

		$.ajaxSetup({
			headers: {
				'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
			}
		});
		
		$.ajax ({
			url: '/get_quantity',
			type: 'POST',
			data: {'color_id': color_id, 'size_id': size_id, 'product_id': product_id}, 
			
			success: function(data){
                $('.qty_sec').html(data);
			}
		})
    })
</script>

{{-- === Show Star === --}}
<script>
    $('.star_cls').click(function(){
        var star = $(this).val();

        $('#star_show').html(star);
    })
</script>

{{-- === Review Done Alert === --}}
@if (session('rev_done'))
<script>
    Swal.fire({
        position: 'middle-middle',
        icon: 'success',
        title: 'Your Review is Collected!',
        showConfirmButton: false,
        timer: 1500
    })
</script>
@endif

{{-- === Review Error Alert === --}}
@if (session('rev_error'))
<script>
    Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Review not Completed! No star/msg provided!',
    })
</script>
@endif

{{-- === Scroll to Error === --}}
<script>
    $('.err').show(function(){
        $(document).ready(function(){
            $("html, body").animate({ 
                scrollTop: $('.err').offset().top -400 
            }, 1000);
        });
    })
</script>

{{-- === All Review Opening === --}}
<script>
    $('.rev_btn').click(function(){
        $('.all_rev').css({
            'opacity': '1',
            'visibility': 'visible',
        });
        $('#shadow').css({
            'display': 'block',
        });
    })
</script>

{{-- === All Review Closing === --}}
<script>
    $('.rev_close_btn').click(function(){
        $('.all_rev').css({
            'opacity': '0',
            'visibility': 'hidden',
        });
        $('#shadow').css({
            'display': 'none',
        });
    })
</script>

<script>
    $(document).ready(function(){

        // Size Guide Modal Functionality
        $("#sizeGuideBtn").click(function(){
            $("#sizeGuideModal").fadeIn(300);
            $("body").addClass("overflow-hidden");
        });

        $("#closeModal").click(function(){
            $("#sizeGuideModal").fadeOut(300);
            $("body").removeClass("overflow-hidden");
        });

        // Close modal when clicking outside of content
        $(document).on('click', function(e){
            if($(e.target).is('#sizeGuideModal')){
                $("#sizeGuideModal").fadeOut(300);
                $("body").removeClass("overflow-hidden");
            }
        });

        // Close modal on escape key
        $(document).keydown(function(e){
            if(e.keyCode === 27){ // ESC key
                $("#sizeGuideModal").fadeOut(300);
                $("body").removeClass("overflow-hidden");
            }
        });

        // ... existing scripts if any ...
    });
</script>
@endsection