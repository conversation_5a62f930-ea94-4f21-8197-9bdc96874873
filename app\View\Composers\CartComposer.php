<?php

namespace App\View\Composers;

use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use App\Models\cartMod;
use App\Models\Product;
use App\Models\Color;
use App\Models\Size;

class CartComposer
{
    /**
     * Bind data to the view.
     *
     * @param  \Illuminate\View\View  $view
     * @return void
     */
    public function compose(View $view)
    {
        $cart_data = $this->getCartData();
        $view->with('cart_data', $cart_data);
    }

    /**
     * Get cart data for both authenticated and guest users
     *
     * @return array
     */
    private function getCartData()
    {
        $cart_items = collect();
        $cart_count = 0;
        $cart_total = 0;

        if (Auth::guard('cust_login')->check()) {
            // Authenticated user - get from database
            $cart_items = cartMod::where('customer_id', Auth::guard('cust_login')->id())->get();
            $cart_count = $cart_items->count();
            
            foreach ($cart_items as $item) {
                $cart_total += $item->relto_product->after_disc * $item->quantity;
            }
        } else {
            // Guest user - get from session
            $guest_cart = session()->get('guest_cart', []);
            $cart_count = count($guest_cart);

            foreach ($guest_cart as $key => $item) {
                $product = Product::find($item['product_id']);
                $color = Color::find($item['color_id']);
                $size = Size::find($item['size_id']);

                if ($product && $color && $size) {
                    $cart_item = (object) [
                        'id' => $key,
                        'product_id' => $item['product_id'],
                        'color_id' => $item['color_id'],
                        'size_id' => $item['size_id'],
                        'quantity' => $item['quantity'],
                        'item_type' => $item['item_type'] ?? null,
                        'customer_picture' => $item['customer_picture'] ?? null,
                        'created_at' => $item['created_at'],
                        'relto_product' => $product,
                        'relto_color' => $color,
                        'relto_size' => $size,
                    ];
                    $cart_items->push($cart_item);
                    $cart_total += $product->after_disc * $item['quantity'];
                }
            }
        }

        return [
            'items' => $cart_items,
            'count' => $cart_count,
            'total' => $cart_total,
            'is_guest' => !Auth::guard('cust_login')->check()
        ];
    }
}
