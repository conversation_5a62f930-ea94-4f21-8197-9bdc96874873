<!DOCTYPE html>
<html lang="zxx">
<head>
	<meta charset="utf-8" />
	<meta name="author" content="Themezhub" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0" />
	<meta name="description" content="IPAC Telecom - Your Trusted Telecom Partner" />
	<meta name="author" content="" />
	<meta name="theme-color" content="#0070DF" />
	<meta name="mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
	<meta name="apple-mobile-web-app-title" content="IPAC Telecom" />
	<meta name="application-name" content="IPAC Telecom" />
	<meta name="csrf-token" content="{{ csrf_token() }}" />

	@php
		$site_info = App\Models\SiteinfoTab::find(1)->first();
	@endphp

	<!-- PWA Icons and Manifest -->
	<link rel="icon" type="image/png" sizes="192x192" href="{{asset('assets/img/logo')}}/{{$site_info->site_icon}}" />
	<link rel="apple-touch-icon" href="{{asset('assets/img/logo')}}/{{$site_info->site_icon}}" />
	<link rel="manifest" href="{{asset('manifest.json')}}" />

	@yield('meta')

	<title>{{$site_info->site_name}}</title>
	<link rel="icon" type="image/png" sizes="16x16" href="{{asset('assets/img/logo')}}/{{$site_info->site_icon}}">

	<!-- Custom CSS -->
	<link href="{{asset('assets/css/plugins/animation.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/bootstrap.min.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/flaticon.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/font-awesome.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/fontawesome.min.css')}}" rel="stylesheet">
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/iconfont.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/ion.rangeSlider.min.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/light-box.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/line-icons.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/slick-theme.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/slick.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/snackbar.min.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/plugins/themify.css')}}" rel="stylesheet">
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link href="{{asset('assets/css/styles.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/style_fend.css')}}" rel="stylesheet">
	<link href="https://cdn.lineicons.com/5.0/lineicons.css" rel="stylesheet">
	<link href="{{asset('assets/css/mobile-responsive.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/mobile-components.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/touch-friendly.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/hamburger-fix.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/hamburger-animation.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/modern-style.css')}}" rel="stylesheet">
	<link href="{{asset('assets/css/rounded-elements.css')}}" rel="stylesheet">

	{{-- === Google Fonts === --}}
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Jost:wght@400;500;600;800&family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">

	{{-- ==== Content HERE ==== --}}
	@yield('header_css')

	<style>
		@media(max-width: 575px){

			#back2Top {
				bottom: 30px;
    			right: unset;
    			left: 30px;
			}

		}

		        /* Style for the banner */
        .language-banner {
            background-color: rgb(0, 162, 255); /* Red background */
            color: white; /* White text */
            padding: 15px;
            text-align: center;
            font-size: 16px;
            position: relative;
            top: 0;
            width: 100%;
            z-index: 1000;
            font-family: Arial, sans-serif;
        }

        /* To ensure the page content isn't hidden behind the banner */
        body {
            margin-top: 0px; /* Adjust this value if necessary */
        }

        .footerlinks a {
            color: white;
            text-decoration: none;
        }

        /* Mobile-First Responsive Design */
        @media (max-width: 768px) {
            /* General Layout */
            body {
                font-size: 14px;
            }

            .container {
                padding: 10px;
                width: 100%;
                max-width: 100%;
            }

            /* Header */
            .header-nav {
                padding: 5px !important;
            }

            .headd-sty-wrap {
                flex-direction: column;
                padding: 10px 0 !important;
            }

            .headd-sty-left {
                width: 100%;
                margin-bottom: 10px;
            }

            .headd-sty-02 {
                width: 100%;
                margin-left: 0 !important;
                margin-top: 10px;
            }

            /* Logo */
            .logo {
                max-width: 120px;
                height: auto;
            }

            /* Search Bar */
            .form-control.custom-height {
                height: 10px !important;  /*this value was 30ox*/
            }

            /* Navigation */
            .nav-menus {
                width: 100%;
            }

            .navigation-landscape .nav-menu > li {
                padding: 5px 0;
            }

            /* Product Cards */
            .shop_grid_caption {
                padding: 10px !important;
            }

            .shop_grid {
                margin-bottom: 15px;
            }

            /* Cart & Wishlist */
            .w3-ch-sideBar {
                width: 90% !important;
            }

            /* Forms */
            .form-group {
                margin-bottom: 15px;
            }

            input, select, textarea {
                font-size: 16px !important; /* Prevents iOS zoom on focus */
            }

            /* Buttons */
            .btn {
                padding: 8px 15px;
                font-size: 14px;
                width: 100%;
                margin-bottom: 10px;
            }

            /* Footer */
            footer {
                padding: 20px 0;
            }

            .footer_widget {
                margin-bottom: 20px;
                text-align: center;
            }

            /* Typography */
            h1 { font-size: 24px !important; }
            h2 { font-size: 20px !important; }
            h3 { font-size: 18px !important; }
            h4 { font-size: 16px !important; }

            /* Navigation Menu */
            .navbar-collapse {
                background: #fff;
                padding: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            /* Social Icons */
            .list-inline-item {
                margin: 0 5px;
            }

            /* Back to Top Button */
            #back2Top {
                right: 20px;
                bottom: 20px;
            }

            /* Language Selector */
            .language-selector-wrapper {
                margin: 10px 0;
                text-align: center;
            }

            /* Cart Counter */
            .dn-counter {
                font-size: 10px;
                padding: 2px 5px;
            }

            /* Product Grid */
            .col-xl-3, .col-lg-3, .col-md-4, .col-6 {
                padding: 5px;
            }

            /* Banner */
            .language-banner {
                position: relative;
                font-size: 12px;
                padding: 8px;
            }

            /* Navigation Menu */
            .nav-menus {
                width: 100%;
            }

            .navigation-landscape .nav-menu > li > a {
                color: #000000 !important;
                font-size: 14px;
                padding: 8px 10px !important;
                white-space: nowrap;
            }

            .mobile_nav {
                display: block !important;
            }

            .nav-toggle {
                display: none !important; /* Hide hamburger menu */
            }

            .navigation-portrait {
                height: auto;
                background: #fff;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }

            .navigation-portrait .nav-menu {
                background: #fff;
            }

            .navigation-portrait .nav-menu > li {
                border-bottom: 1px solid #eee;
            }

            .navigation-portrait .nav-menu > li:last-child {
                border-bottom: none;
            }
        }

        /* Tablet Optimization */
        @media (min-width: 769px) and (max-width: 1024px) {
            .container {
                padding: 15px;
            }

            .headd-sty-02 {
                width: 60%;
            }

            .nav-menus {
                width: auto;
            }
        }

        /* Touch Device Optimization */
        @media (hover: none) {
            .btn:hover {
                opacity: 1;
            }

            .nav-link {
                padding: 12px 8px;
            }
        }

        /* Mobile Navigation */
        @media (max-width: 768px) {
            .nav-toggle {
                display: none !important; /* Hide hamburger menu */
            }

            .navigation-landscape .nav-menus-wrapper {
                display: flex !important;
                position: static;
                background: transparent;
                overflow-x: auto;
                width: 100%;
            }

            .navigation-landscape .nav-menu {
                display: flex;
                flex-direction: row;
                width: 100%;
                justify-content: space-between;
                background: transparent !important;
            }

            .navigation-landscape .nav-menu > li {
                border: none !important;
                padding: 0 !important;
            }

            .navigation-landscape .nav-menu > li > a {
                color: white !important;
                font-size: 14px;
                padding: 8px 10px !important;
                white-space: nowrap;
            }

            #navigation {
                background: #f0f0f0;
                padding: 5px 0;
            }

            .nav-menus-wrapper-close-button {
                display: none !important;
            }
        }
	</style>

	<style>
		/* Override navigation background with light gray */
		#navigation,
		.navigation-landscape,
		div[style*="background: linear-gradient"] {
			background: #f0f0f0 !important;
		}

		/* Ensure navigation text is black on all devices */
		.navigation-landscape .nav-menu > li > a,
		.navigation-portrait .nav-menu > li > a {
			color: #000000 !important;
		}

		/* Shop dropdown menu styles */
		.shop-dropdown {
			display: none;
			position: absolute;
			background: white;
			min-width: 200px;
			box-shadow: 0 2px 5px rgba(0,0,0,0.1);
			z-index: 1000;
			border-radius: 4px;
			padding: 8px 0;
		}

		.navigation-landscape .nav-menu > li:hover .shop-dropdown {
			display: block;
		}

		.shop-dropdown li {
			list-style: none;
		}

		.shop-dropdown li a {
			display: block;
			padding: 8px 15px;
			color: #333 !important;
			text-decoration: none;
			transition: background-color 0.2s;
		}

		.shop-dropdown li a:hover {
			background-color: #f8f9fa;
		}

		/* Force hide mobile elements on desktop */
		@media (min-width: 992px) {
			.mobile-header,
			.mobile-nav,
			.mobile-nav-overlay,
			.hamburger-menu {
				display: none !important;
				visibility: hidden !important;
				opacity: 0 !important;
				pointer-events: none !important;
			}
		}

		/* Ensure hamburger menu is only visible on mobile */
		@media (max-width: 991px) {
			.hamburger-menu {
				display: flex !important;
				visibility: visible !important;
				opacity: 1 !important;
				pointer-events: auto !important;
			}

			/* 🔧 Hide hamburger menu when nav is open */
			.hamburger-menu.active {
				display: none !important;
			}
			/* Reset hamburger menu to closed state */
			.hamburger-menu.active span:nth-child(1),
			.hamburger-menu.active span:nth-child(4) {
				top: initial;
				width: initial;
				left: initial;
			}

			.hamburger-menu.active span:nth-child(2),
			.hamburger-menu.active span:nth-child(3) {
				transform: initial;
			}

			/* Force mobile navigation menu to be hidden by default */
			.mobile-nav {
				position: fixed !important;
				top: 0 !important;
				left: -280px !important;
				width: 280px !important;
				height: 100% !important;
				background: #fff !important;
				z-index: 1000 !important;
				overflow-y: auto !important;
				transition: 0.3s ease-in-out !important;
				box-shadow: 5px 0 15px rgba(0,0,0,0.1) !important;
				transform: translateX(-100%) !important;
			}

			/* Only show mobile navigation menu when it has the active class */
			.mobile-nav.active {
				left: 0 !important;
				transform: translateX(0) !important;
			}

			/* Ensure mobile navigation overlay is hidden by default */
			.mobile-nav-overlay {
				display: none !important;
			}

			/* Only show mobile navigation overlay when it has the active class */
			.mobile-nav-overlay.active {
				display: block !important;
			}

			.hamburger-menu.active {
				display: none;
			}

		}
	</style>

</head>

<body>
<!-- Immediate script to ensure hamburger menu is closed on page load -->
<script>
(function() {
    // Force hamburger menu to closed state immediately
    var hamburgerMenu = document.querySelector('.hamburger-menu');
    var mobileNav = document.querySelector('.mobile-nav');
    var mobileNavOverlay = document.querySelector('.mobile-nav-overlay');
    var mobileNavClose = document.querySelector('.mobile-nav-close');

    // Force remove active class from all elements
    if (hamburgerMenu) {
        // Ensure hamburger menu is not in active state
        hamburgerMenu.classList.remove('active');

        if (window.innerWidth >= 992) {
            hamburgerMenu.style.display = 'none';
        } else {
            // Ensure hamburger menu is visible on mobile
            hamburgerMenu.style.display = 'flex';
            hamburgerMenu.style.visibility = 'visible';
            hamburgerMenu.style.opacity = '1';
            hamburgerMenu.style.pointerEvents = 'auto';
        }
    }

    if (mobileNav) {
        // Force hide mobile navigation menu
        mobileNav.classList.remove('active');
        mobileNav.style.left = '-280px';
        mobileNav.style.transform = 'translateX(-100%)';

        if (window.innerWidth >= 992) {
            mobileNav.style.display = 'none';
        }
    }

    if (mobileNavOverlay) {
        mobileNavOverlay.classList.remove('active');
        mobileNavOverlay.style.display = 'none';

        if (window.innerWidth >= 992) {
            mobileNavOverlay.style.display = 'none';
        }
    }

    // Add direct event listener to close button
    if (mobileNavClose) {
        mobileNavClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (hamburgerMenu) hamburgerMenu.classList.remove('active');
            if (mobileNav) {
                mobileNav.classList.remove('active');
                mobileNav.style.left = '-280px';
                mobileNav.style.transform = 'translateX(-100%)';
            }
            if (mobileNavOverlay) {
                mobileNavOverlay.classList.remove('active');
                mobileNavOverlay.style.display = 'none';
            }
            document.body.style.overflow = '';
        });
    }
})();
</script>

<!-- Start of Tawk.to Script -->

<script type="text/javascript">
    var Tawk_API = Tawk_API || {};
    var Tawk_LoadStart = new Date();

    (function () {
        var s1 = document.createElement("script");
        var s0 = document.getElementsByTagName("script")[0];

        s1.async = true;
        s1.src = 'https://embed.tawk.to/674b87ab4304e3196aeaef33/1idvgju73';
        s1.charset = 'UTF-8';
        s1.setAttribute('crossorigin', '*');

        s0.parentNode.insertBefore(s1, s0);
    })();
</script>

<!-- End of Tawk.to Script -->
<!--
<script src="https://cdn.botpress.cloud/webchat/v2.2/inject.js"></script>
<script src="https://files.bpcontent.cloud/2024/12/01/14/20241201144636-NPUET7WJ.js"></script>
    -->



	<!-- ============================================================== -->
	<!-- Preloader - style you can find in spinners.css -->
	<!-- ============================================================== -->

	<div class="preloader"></div>
	<div id="loader"></div>
	<div id="shadow"></div>

	<!-- ============================================================== -->
	<!-- Main wrapper - style you can find in pages.scss -->
	<!-- ============================================================== -->
	<div id="main-wrapper">
		@if(session('user_login'))
			<div class="shw" style = "visibility: visible"></div>
			<div class="alert_part" style = "visibility: visible">
				<div class="alert">
					<h2>Hello</h2>
					<div class="my-3">
						<h3>{{Auth::guard('cust_login')->user()->name}}</h3>
					</div>
					<img src="{{asset('assets/img/user.png')}}" width="80px">
				</div>
				<img src="{{asset('assets/img/signin_dash.png')}}" class="alert_bg" width="400px">
			</div>
			{{header("refresh: 2")}}
		@endif

		<!-- ============================================================== -->
		<!-- Top header  -->
		<!-- ============================================================== -->
		<!-- Top Header (visible only on desktop) -->
		<div class="py-2 br-bottom header_one d-none d-lg-block">
			<div class="container">
				<div class="row">

					<div class="col-xl-7 col-lg-6 col-md-6 col-sm-12 hide-ipad">
						<div class="top_second"><p class="medium text-muted m-0 p-0"><i class="fal fa-at"></i>
							@if (session('lang_ben'))
								মেইল:
							@elseif (session('lang_fin'))
								Sähköposti:
							@else
								Email:
							@endif
							<a href="mailto:<EMAIL>" class="medium text-dark text-underline"><EMAIL></a></p>
						</div>
					</div>

					<!-- Right Menu -->
					<div class="col-xl-5 col-lg-6 col-md-12 col-sm-12">
						<!-- Choose Language -->
						<div class="language-selector-wrapper dropdown js-dropdown float-right mr-3">
							<a class="popup-title" href="javascript:void(0)" data-toggle="dropdown" title="Language" aria-label="Language dropdown">
								<span class="hidden-xl-down medium text-muted">Language:</span>
								@if (session('lang_ben'))
									<img src="{{asset('assets/img/555.jpg')}}" alt="bn" width="16" height="11"/>
									<span class="iso_code medium text-muted">{{'Bengali'}}</span>
								@elseif (session('lang_fin'))
									<img src="{{asset('assets/img/finnish-flag.jpg')}}" alt="fi" width="16" height="11"/>
									<span class="iso_code medium text-muted">{{'Finnish'}}</span>
								@else
									<img src="{{asset('assets/img/1.jpg')}}" alt="en" width="16" height="11" />
									<span class="iso_code medium text-muted">{{'English'}}</span>
								@endif

								<i class="fa fa-angle-down medium text-muted"></i>
							</a>
							<ul class="dropdown-menu popup-content link">
								<li class="current"><a href="{{route('lang.eng')}}" class="dropdown-item medium text-muted"><img src="{{asset('assets/img/1.jpg')}}" alt="en" width="16" height="11" /><span>English</span></a></li>

								<li><a href="{{route('lang.ben')}}" class="dropdown-item medium text-muted"><img src="{{asset('assets/img/555.jpg')}}" alt="bn" width="16" height="11" /><span>Bengali</span></a></li>

								<li><a href="{{route('lang.fin')}}" class="dropdown-item medium text-muted"><img src="{{asset('assets/img/finnish-flag.jpg')}}" alt="fi" width="16" height="11" /><span>Finnish</span></a></li>
							</ul>
						</div>

						{{-- === Profile/Logout === --}}
						<div class="language-selector-wrapper dropdown js-dropdown float-right mr-5">
							@auth('cust_login')
								<a class="popup-title" href="javascript:void(0)" data-toggle="dropdown" title="" aria-label="">
									<span class="iso_code medium text-danger" style="font-size: 16px">
										{{Auth::guard('cust_login')->user()->name}}
									</span>
									<i class="fa fa-angle-down medium text-muted"></i>
								</a>
								<ul class="dropdown-menu popup-content link">
									<li><a class="dropdown-item" href="{{route('customer.profile')}}">
										<i class="fas fa-user-circle mr-2"></i>
										@if (session('lang_ben'))
											প্রোফাইল
										@elseif (session('lang_fin'))
											Profiili
										@else
											Profile
										@endif
									</a></li>
									<li><a class="dropdown-item" href="{{route('customer.logout')}}"><i class="far fa-sign-out mr-2"></i>
										@if (session('lang_ben'))
											প্রস্থান
										@elseif (session('lang_fin'))
											Kirjaudu ulos
										@else
											Logout
										@endif
									</a></li>
								</ul>
							@else
								<a href="{{route('customer_login')}}" class="text-muted medium"><i class="fad fa-users mr-2"></i>
									@if (session('lang_ben'))
										সাইন ইন / নিবন্ধন
									@elseif (session('lang_fin'))
										Kirjaudu sisään / Rekisteröidy
									@else
										Sign In / Register
									@endif
								</a>
							@endauth
						</div>
					</div>

				</div>
			</div>
		</div>

		<!-- Desktop Header (visible only on desktop) -->
		<div class="headd-sty header desktop-header d-none d-lg-block">
			<div class="container">
				<div class="row">
					<div class="col-xl-12 col-lg-12 col-md-12">
						<div class="headd-sty-wrap d-flex align-items-center justify-content-between py-3">
							<!-- Left side with logo and search -->
							<div class="headd-sty-left d-flex align-items-center">
								<!-- Logo -->
								<div class="headd-sty-01">
									<a class="nav-brand py-0" href="https://ipactelecom.xyz">
										<img src="{{asset('assets/img/logo')}}/{{$site_info->site_logo}}" class="logo w-100" alt="" />
									</a>
								</div>

								<!-- Desktop Search Form -->
								<div class="headd-sty-02 ml-3">
									<form class="bg-white rounded-md border-bold" method="GET" action="{{ route('shop_page') }}">
										<div class="input-group">
											<input
												type="text"
												name="inp"
												class="form-control custom-height b-0"
												placeholder="@if (session('lang_ben'))পণ্য খুঁজুন
												@elseif (session('lang_fin'))Tuotteiden haku
												@else
Search for products...
												@endif
												"
												id="master_inp"
												value="{{request()->get('inp')}}"
											/>
											<div class="input-group-append">
												<div class="input-group-text">
													<button class="btn bg-white text-danger custom-height rounded px-3" type="submit">
														<i class="fas fa-search"></i>
													</button>
												</div>
											</div>
										</div>
									</form>
								</div>
							</div>
							<div class="headd-sty-last">
								<ul class="nav-menu nav-menu-social align-to-right align-items-center d-flex">
									<li>
										<div class="call d-flex align-items-center text-left">
											<i class="lni lni-phone fs-xl"></i>
											<span class="text-muted small ml-3">
												@if (session('lang_ben'))
													আমাদের কল করুন:
												@elseif (session('lang_fin'))
													Soita meille:
												@else
													Call Us Now:
												@endif
												<strong class="d-block text-dark fs-md">({{$site_info->site_ph_code}}) {{$site_info->site_phone}}</strong>
											</span>
										</div>
									</li>

									{{-- === Open Wishlist === --}}
									@php
										$act_wish_qty = 0;
									@endphp
									@foreach (App\Models\WishTable::where('customer_id', Auth::guard('cust_login')->id())->get() as $wish_qty)
										@if ($wish_qty->relto_product()->exists())
											@php
												$act_wish_qty += 1;
											@endphp
										@endif
									@endforeach
									<li>
										<a href="#" onclick="openWishlist()">
											<i class="far fa-heart fs-lg"></i><span class="dn-counter bg-success">{{$act_wish_qty}}</span>
										</a>
									</li>

									{{-- === Open Cart === --}}
									<li>
										<a href="#" onclick="openCart()">
											<div class="d-flex align-items-center justify-content-between">
												<i class="fas fa-shopping-basket fs-lg"></i><span class="dn-counter theme-bg cart-count">
													@if(Auth::guard('cust_login')->check())
														{{App\Models\cartMod::where('customer_id', Auth::guard('cust_login')->id())->count()}}
													@else
														{{count(session()->get('guest_cart', []))}}
													@endif
												</span>
											</div>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Mobile Header (visible only on mobile) -->
		<div class="mobile-header d-lg-none">
			<!-- Mobile Top Bar with Sign In/Register and Language Selector -->
			<div class="mobile-top-bar py-2">
				<div class="container">
					<div class="row">
						<div class="col-12">
							<div class="d-flex justify-content-between align-items-center">
								<!-- Sign In/Register -->
								<div class="mobile-signin">
									@auth('cust_login')
										<a href="{{route('customer.profile')}}" class="text-muted medium">
											<i class="fad fa-user mr-2"></i>
											{{Auth::guard('cust_login')->user()->name}}
										</a>
									@else
										<a href="{{route('customer_login')}}" class="text-muted medium">
											<i class="fad fa-users mr-2"></i>
											@if (session('lang_ben'))
												সাইন ইন / নিবন্ধন
											@elseif (session('lang_fin'))
												Kirjaudu sisään / Rekisteröidy
											@else
												Sign In / Register
											@endif
										</a>
									@endauth
								</div>

								<!-- Language Selector -->
								<div class="mobile-language">
									<div class="language-selector-wrapper dropdown js-dropdown">
										<a class="popup-title" href="javascript:void(0)" data-toggle="dropdown" title="Language" aria-label="Language dropdown">
											@if (session('lang_ben'))
												<img src="{{asset('assets/img/555.jpg')}}" alt="bn" width="16" height="11"/>
												<span class="iso_code medium text-muted">{{'Bengali'}}</span>
											@elseif (session('lang_fin'))
												<img src="{{asset('assets/img/finnish-flag.jpg')}}" alt="fi" width="16" height="11"/>
												<span class="iso_code medium text-muted">{{'Finnish'}}</span>
											@else
												<img src="{{asset('assets/img/1.jpg')}}" alt="en" width="16" height="11" />
												<span class="iso_code medium text-muted">{{'English'}}</span>
											@endif
											<i class="fa fa-angle-down medium text-muted"></i>
										</a>
										<ul class="dropdown-menu popup-content link">
											<li class="current"><a href="{{route('lang.eng')}}" class="dropdown-item medium text-muted"><img src="{{asset('assets/img/1.jpg')}}" alt="en" width="16" height="11" /><span>English</span></a></li>
											<li><a href="{{route('lang.ben')}}" class="dropdown-item medium text-muted"><img src="{{asset('assets/img/555.jpg')}}" alt="bn" width="16" height="11" /><span>Bengali</span></a></li>
											<li><a href="{{route('lang.fin')}}" class="dropdown-item medium text-muted"><img src="{{asset('assets/img/finnish-flag.jpg')}}" alt="fi" width="16" height="11" /><span>Finnish</span></a></li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Mobile Main Header -->
			<div class="mobile-main-header py-2">
				<div class="container">
					<div class="row">
						<div class="col-12">
							<div class="d-flex align-items-center justify-content-between">
								<!-- Left side with hamburger menu and logo -->
								<div class="d-flex align-items-center">
									<!-- Hamburger Menu -->
									<button type="button" class="hamburger-menu mr-3" style="display: flex !important; visibility: visible !important; opacity: 1 !important; pointer-events: auto !important; position: relative !important; width: 40px !important; height: 40px !important; margin-right: 15px !important; z-index: 1001 !important; cursor: pointer !important; background: transparent !important; border: none !important; padding: 0 !important; outline: none !important;">
										<div class="hamburger-inner" style="position: relative !important; width: 100% !important; height: 100% !important;">
											<span class="hamburger-bar bar1" style="display: block !important; position: absolute !important; height: 3px !important; width: 25px !important; background: #333 !important; border-radius: 3px !important; opacity: 1 !important; left: 8px !important; top: 12px !important; transition: all 0.3s ease-in-out !important;"></span>
											<span class="hamburger-bar bar2" style="display: block !important; position: absolute !important; height: 3px !important; width: 25px !important; background: #333 !important; border-radius: 3px !important; opacity: 1 !important; left: 8px !important; top: 19px !important; transition: all 0.3s ease-in-out !important;"></span>
											<span class="hamburger-bar bar3" style="display: block !important; position: absolute !important; height: 3px !important; width: 25px !important; background: #333 !important; border-radius: 3px !important; opacity: 1 !important; left: 8px !important; top: 26px !important; transition: all 0.3s ease-in-out !important;"></span>
										</div>
									</button>

									<!-- Logo -->
									<div class="mobile-logo">
										<a href="https://ipactelecom.xyz">
											<img src="{{asset('assets/img/logo')}}/{{$site_info->site_logo}}" class="logo" alt="" />
										</a>
									</div>
								</div>

								<!-- Right side with search, wishlist, and cart icons -->
								<div class="d-flex align-items-center">
									<!-- Mobile Search Button -->
									<button class="mobile-search-btn mr-3">
										<i class="fas fa-search"></i>
									</button>

									<!-- Header Icons (Wishlist and Cart) -->
									<div class="header-icons">
										{{-- === Open Wishlist === --}}
										<a href="#" onclick="openWishlist()" class="mr-2">
											<i class="far fa-heart"></i>
											<span class="dn-counter bg-success">{{$act_wish_qty}}</span>
										</a>

										{{-- === Open Cart === --}}
										<a href="#" onclick="openCart()">
											<i class="fas fa-shopping-basket"></i>
											<span class="dn-counter theme-bg cart-count">
												@if(Auth::guard('cust_login')->check())
													{{App\Models\cartMod::where('customer_id', Auth::guard('cust_login')->id())->count()}}
												@else
													{{count(session()->get('guest_cart', []))}}
												@endif
											</span>
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Mobile Search Form (hidden by default, shown when search button is clicked) -->
			<div class="mobile-search-form">
				<form method="GET" action="{{ route('shop_page') }}">
					<div class="input-group">
						<input
							type="text"
							name="inp"
							class="form-control"
							placeholder="@if (session('lang_ben'))পণ্য খুঁজুন
							@elseif (session('lang_fin'))Tuotteiden haku
							@else
Search for products...
							@endif
							"
							value="{{request()->get('inp')}}"
						/>
						<div class="input-group-append">
							<button class="btn btn-danger" type="submit">
								<i class="fas fa-search"></i>
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<!-- Mobile Navigation Menu -->
		<div class="mobile-nav" style="left: -280px; transform: translateX(-100%);">
			 <div class="mobile-nav-header">
				<!-- <img src="{{asset('assets/img/logo')}}/{{$site_info->site_logo}}" class="logo" alt="" /> -->
				<!-- <button class="mobile-nav-close" type="button" aria-label="Close menu"> -->
					<!-- <i class="fas fa-times"></i>
				</button> -->
			</div>
			<div class="mobile-nav-menu">
				<ul>
					<li>
						<a href="{{route('home_page')}}">
							@if (session('lang_ben'))
								হোম
							@elseif (session('lang_fin'))
								Etusivu
							@else
								Home
							@endif
						</a>
					</li>
					<li>
						<a href="{{route('shop_page')}}">
							@if (session('lang_ben'))
								দোকান
							@elseif (session('lang_fin'))
								Kauppa
							@else
								Shop
							@endif
						</a>
					</li>
					<li>
						@php
							$stickerSubcategoriesMobile = App\Models\Subcategory::where('sub_cata_name', 'LIKE', '%sticker%')->get();
							$subcategoryIdsMobile = $stickerSubcategoriesMobile->pluck('id')->toArray();
							$stickerLinkMobile = !empty($subcategoryIdsMobile) ? route('shop_page') . '?subcate=' . implode(',', $subcategoryIdsMobile) : route('shop_page');
						@endphp
						<a href="{{$stickerLinkMobile}}">
							@if (session('lang_ben'))
								কাস্টমাইজেবল স্টিকার
							@elseif (session('lang_fin'))
								Mukautettavat tarrat
							@else
								Customizable Stickers
							@endif
						</a>
					</li>
					<li>
						<a href="{{route('about_page')}}">
							@if (session('lang_ben'))
								আমাদের সম্পর্কে
							@elseif (session('lang_fin'))
								Tietoa meistä
							@else
								About Us
							@endif
						</a>
					</li>
					<li>
						<a href="{{route('contact_page')}}">
							@if (session('lang_ben'))
								যোগাযোগ
							@elseif (session('lang_fin'))
								Yhteystiedot
							@else
								Contact
							@endif
						</a>
					</li>
					<li>
						<a href="{{route('repair.service')}}">
							@if (session('lang_ben'))
								মেরামত সেবা
							@elseif (session('lang_fin'))
								Korjauspalvelu
							@else
								Repair Service
							@endif
						</a>
					</li>
					<li>
						<!-- <a href="{{route('customize')}}">
							@if (session('lang_ben'))কাস্টমাইজেশন স্টিকার
							@elseif (session('lang_fin'))Koristelutarrat
							@else Customization Stickers
							@endif
						</a> -->
					</li>
					<li>
						<a href="pwa-install-guide">
							@if (session('lang_ben'))পিডব্লিউএ ইনস্টলেশন গাইড
							@elseif (session('lang_fin'))PWA-asennusopas
							@else PWA Installation Guide
							@endif
						</a>
					</li>
				</ul>
			</div>
		</div>

		<!-- Mobile Navigation Overlay -->
		<div class="mobile-nav-overlay"></div>

		<!-- Start Navigation (visible only on desktop) -->
		<div style="background: #f0f0f0; padding: 10px;" class="d-none d-lg-block">

			<div class="container">
				<nav id="navigation" class="navigation navigation-landscape">
					<div class="nav-menus-wrapper">
						<ul class="nav-menu">
							<li><a href="{{route('home_page')}}" style="color: #000000; text-decoration: none;">
								@if (session('lang_ben'))
									হোম
								@elseif (session('lang_fin'))
									Etusivu
								@else
									Home
								@endif
							</a></li>
							<li>
								<a href="{{route('shop_page')}}" style="color: #000000; text-decoration: none;">
									@if (session('lang_ben'))
										দোকান▿
									@elseif (session('lang_fin'))
										Kauppa▿
									@else
										Shop▿
									@endif
								</a>
								<ul class="shop-dropdown">
									@foreach($categories as $category)
										<li>
											<a href="{{route('shop_page')}}?cate={{$category->id}}">
												@if (session('lang_fin'))
													{{ $category->cata_name_fin ?? $category->cata_name }}
												@elseif (session('lang_ben'))
													{{ $category->cata_name_ben ?? $category->cata_name }}
												@else
													{{ $category->cata_name }}
												@endif
											</a>
										</li>
									@endforeach
								</ul>
							</li>
							<li>
								@php
									$stickerSubcategoriesDesktop = App\Models\Subcategory::where('sub_cata_name', 'LIKE', '%sticker%')->get();
									$subcategoryIdsDesktop = $stickerSubcategoriesDesktop->pluck('id')->toArray();
									$stickerLink = !empty($subcategoryIdsDesktop) ? route('shop_page') . '?subcate=' . implode(',', $subcategoryIdsDesktop) : route('shop_page');
								@endphp
								<a href="{{$stickerLink}}" style="color: #000000; text-decoration: none;">
									@if (session('lang_ben'))
										কাস্টমাইজেবল স্টিকার
									@elseif (session('lang_fin'))
										Mukautettavat tarrat
									@else
										Customizable Stickers
									@endif
								</a>
							</li>
							<li><a href="{{route('repair.service')}}" style="color: #000000; text-decoration: none;">
								@if (session('lang_ben'))
									মেরামত সেবা
								@elseif (session('lang_fin'))
									Korjauspalvelu
								@else
									Repair Service
								@endif
							</a></li>
							<!-- <li><a href="{{route('customize')}}">
								@if (session('lang_ben'))কাস্টমাইজেশন স্টিকার
								@elseif (session('lang_fin'))Koristelutarrat
								@else Customization Stickers
								@endif
							</a></li> -->
							<li><a href="{{route('about_page')}}" style="color: #000000; text-decoration: none;">
								@if (session('lang_ben'))
									আমাদের সম্পর্কে
								@elseif (session('lang_fin'))
									Tietoa meistä
								@else
									About Us
								@endif
							</a></li>
							<li><a href="{{route('contact_page')}}" style="color: #000000; text-decoration: none;">
								@if (session('lang_ben'))
									যোগাযোগ
								@elseif (session('lang_fin'))
									Yhteystiedot
								@else
									Contact
								@endif
							</a></li>
							<li><a href="{{route('pwa.install.guide')}}" style="color: #000000; text-decoration: none;">
								@if (session('lang_ben'))
									পিডব্লিউএ ইনস্টলেশন গাইড
								@elseif (session('lang_fin'))
									PWA-asennusopas
								@else
									PWA Installation Guide
								@endif
							</a></li>

						</ul>
					</div>
				</nav>
			</div>
		</div>
		<!-- End Navigation -->
		<div class="clearfix"></div>
		<!-- ============================================================== -->
		<!-- Top header  -->
		<!-- ============================================================== -->


		{{-- ==== Content HERE ==== --}}
		@yield('content')




		<!-- ======================= Customer Features ======================== -->
		<section class="px-0 py-3 br-top">
			<div class="container">
				<div class="row">

					<div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
						<div class="d-flex align-items-center justify-content-start py-2">
							<div class="d_ico">
								<i class="fas fa-shopping-basket"></i>
							</div>
							<div class="d_capt">
								<h5 class="mb-0">
									@if (session('lang_ben'))
										বিনামূল্যে শিপিং
									@elseif (session('lang_fin'))
										Ilmainen toimitus
									@else
										Free Shipping
									@endif
								</h5>
								<span class="text-muted">
									@if (session('lang_ben'))
										প্রতি অর্ডারে ১০ ডলার পর্যন্ত সীমাবদ্ধ
									@elseif (session('lang_fin'))
										Rajoitettu 10 euroon per tilaus
									@else
										Capped at $10 per order
									@endif
								</span>
							</div>
						</div>
					</div>

					<div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
						<div class="d-flex align-items-center justify-content-start py-2">
							<div class="d_ico">
								<i class="far fa-credit-card"></i>
							</div>
							<div class="d_capt">
								<h5 class="mb-0">
									@if (session('lang_ben'))
										নিরাপদ পেমেন্ট
									@elseif (session('lang_fin'))
										Turvalliset maksut
									@else
										Secure Payments
									@endif
								</h5>
								<span class="text-muted">
									@if (session('lang_ben'))
										৬ মাস পর্যন্ত কিস্তিতে
									@elseif (session('lang_fin'))
										Jopa 6 kuukauden osamaksuilla
									@else
										Up to 6 months installments
									@endif
								</span>
							</div>
						</div>
					</div>

					<div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
						<div class="d-flex align-items-center justify-content-start py-2">
							<div class="d_ico">
								<i class="fas fa-shield-alt"></i>
							</div>
							<div class="d_capt">
								<h5 class="mb-0">
									@if (session('lang_ben'))
										১৪-দিনের রিটার্ন
									@elseif (session('lang_fin'))
										14 päivän palautusoikeus
									@else
										14-Days Returns
									@endif
								</h5>
								<span class="text-muted">
									@if (session('lang_ben'))
										পুরো আত্মবিশ্বাসের সাথে শপিং করুন
									@elseif (session('lang_fin'))
										Osta täydellä luottamuksella
									@else
										Shop with fully confidence
									@endif
								</span>
							</div>
						</div>
					</div>

					<div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
						<div class="d-flex align-items-center justify-content-start py-2">
							<div class="d_ico">
								<i class="fas fa-headphones-alt"></i>
							</div>
							<div class="d_capt">
								<h5 class="mb-0">
									@if (session('lang_ben'))
										২৪×৭ সম্পূর্ণ সমর্থন
									@elseif (session('lang_fin'))
										24/7 Täysi tuki
									@else
										24x7 Fully Support
									@endif
								</h5>
								<span class="text-muted">
									@if (session('lang_ben'))
										বন্ধুত্বপূর্ণ সহায়তা পান
									@elseif (session('lang_fin'))
										Saa ystävällistä tukea
									@else
										Get friendly support
									@endif
								</span>
							</div>
						</div>
					</div>

				</div>
			</div>
		</section>
		<!-- ======================= Customer Features ======================== -->

		<!-- ============================ Footer Start ================================== -->
		<footer style="background: linear-gradient(to bottom,rgb(227, 227, 227),rgb(238, 238, 238)); padding: px;">
			<div class="footer-middle">
				<div class="container">
					<div class="row">

						<div class="col-xl-3 col-lg-3 col-md-3 col-sm-12">
							<div class="footer_widget">
								<img src="{{asset('assets/img/logo')}}/{{$site_info->site_logo}}" class="img-footer small mb-2" alt="" />

								<div class="address mt-3" style="color: black; text-decoration: none;">
									{{$site_info->site_add1}}, <br>{{$site_info->site_add2}}
								</div>
								<div class="address mt-3">
									<a href="tel:{{$site_info->site_ph_code}}{{$site_info->site_phone}}">({{$site_info->site_ph_code}}) {{$site_info->site_phone}}</a><br>
									<a href="mailto:{{$site_info->site_email}}" class="text-danger"><u>{{$site_info->site_email}}</u></a>
								</div>
								<div class="address mt-3">
									<ul class="list-inline">
										<li class="list-inline-item"><a href="https://www.facebook.com/ipacbd"><i class="lni lni-facebook"></i></a></li>
										<li class="list-inline-item"><a href="https://twitter.com/ipactelecom.xyz"><i class="lni lni-twitter-old"></i></a></li>
										<li class="list-inline-item"><a href="https://www.youtube.com/@ipactelecom6198"><i class="lni lni-youtube"></i></a></li>
										<li class="list-inline-item"><a href="https://www.instagram.com/ipactelecom"><i class="lni lni-instagram"></i></a></li>
										<li class="list-inline-item"><a href="https://www.linkedin.com/ipactelecom.xyz"><i class="lni lni-linkedin"></i></a></li>
										<li class="list-inline-item"><a href="https://www.tiktok.com/@ipactelecom"><i class="lni lni-tiktok"></i></a></li>
									</ul>
								</div>
								<div class="address mt-3">
									<h5 class="fs-sm text-light">
										@if (session('lang_ben'))
											নিরাপদ পেমেন্ট
										@elseif (session('lang_fin'))
											Turvalliset maksut
										@else
											Secure Payments
										@endif
									</h5>
									<div class="scr_payment"><img src="assets/img/card.png" class="img-fluid" alt="" /></div>
								</div>
							</div>
						</div>

						<div class="col-xl-2 col-lg-2 col-md-2 col-sm-12">
							<div class="footer_widget">
								<h4 class="widget_title">
									@if (session('lang_ben'))
										সমর্থন
									@elseif (session('lang_fin'))
										Tuki
									@else
										Supports
									@endif
								</h4>
								<ul class="footerlinks">
									<li><a href="{{route('contact_page')}}" class="footerlinks">
										@if (session('lang_ben'))
											যোগাযোগ করুন
										@elseif (session('lang_fin'))
											Ota yhteyttä
										@else
											Contact Us
										@endif
									</a></li>
									<li><a href="{{route('about_page')}}" class="footerlinks">
										@if (session('lang_ben'))
											আমাদের সম্পর্কে পৃষ্ঠা
										@elseif (session('lang_fin'))
											Tietoa meistä
										@else
											About Page
										@endif
									</a></li>

									<li><a href="{{route('faq_page')}}" class="footerlinks">
										@if (session('lang_ben'))
											প্রায়শই জিজ্ঞাসিত প্রশ্ন
										@elseif (session('lang_fin'))
											UKK-sivu
										@else
											FAQ's Page
										@endif
									</a></li>

									<li><a href="/pwa-install-guide" class="footerlinks">
										@if (session('lang_ben'))
											পিডব্লিউএ ইনস্টলেশন গাইড
										@elseif (session('lang_fin'))
											PWA-asennusopas
										@else
											PWA Installation Guide
										@endif
									</a></li>

								</ul>
							</div>
						</div>

						<div class="col-xl-2 col-lg-2 col-md-2 col-sm-12">
							<div class="footer_widget">
								<h4 class="widget_title">
									@if (session('lang_ben'))
										দোকান
									@elseif (session('lang_fin'))
										Kauppa
									@else
										Shop
									@endif
								</h4>
								<ul class="footer-menu">
									<li><a href="#" class="footerlinks">
										@if (session('lang_ben'))
											পুরুষদের শপিং
										@elseif (session('lang_fin'))
											Miesten ostokset
										@else
											Men's Shopping
										@endif
									</a></li>
									<li><a href="#" class="footerlinks">
										@if (session('lang_ben'))
											মহিলাদের শপিং
										@elseif (session('lang_fin'))
											Naisten ostokset
										@else
											Women's Shopping
										@endif
									</a></li>
									<li><a href="#" class="footerlinks">
										@if (session('lang_ben'))
											শিশুদের শপিং
										@elseif (session('lang_fin'))
											Lasten ostokset
										@else
											Kids's Shopping
										@endif
									</a></li>
									<li><a href="#" class="footerlinks">
										@if (session('lang_ben'))
											আসবাবপত্র
										@elseif (session('lang_fin'))
											Huonekalut
										@else
											Furniture
										@endif
									</a></li>
									<li><a href="{{route('coupon.view')}}" class="footerlinks">
										@if (session('lang_ben'))
											ছাড়
										@elseif (session('lang_fin'))
											Alennukset
										@else
											Discounts
										@endif
									</a></li>
								</ul>
							</div>
						</div>

						<div class="col-xl-2 col-lg-2 col-md-2 col-sm-12">
							<div class="footer_widget">
								<h4 class="widget_title">
									@if (session('lang_ben'))
										কোম্পানি
									@elseif (session('lang_fin'))
										Yritys
									@else
										Company
									@endif
								</h4>
								<ul class="footer-menu">
									<li><a href="{{route('about_page')}}" class="footerlinks">
										@if (session('lang_ben'))
											আমাদের সম্পর্কে
										@elseif (session('lang_fin'))
											Tietoa meistä
										@else
											About
										@endif
									</a></li>

									<li><a href="{{route('customer_login')}}" class="footerlinks">
										@if (session('lang_ben'))
											লগইন
										@elseif (session('lang_fin'))
											Kirjaudu sisään
										@else
											Login
										@endif
									</a></li>
								</ul>
							</div>
						</div>

						<div class="col-xl-3 col-lg-3 col-md-3 col-sm-12">
							<div class="footer_widget">
								<h4 class="widget_title">
									@if (session('lang_ben'))
										সাবস্ক্রাইব
									@elseif (session('lang_fin'))
										Tilaa
									@else
										Subscribe
									@endif
								</h4>
								<p class="footerlinks">
									@if (session('lang_ben'))
										আপডেট, হট ডিল, ছাড় সরাসরি আপনার ইনবক্সে প্রতিদিন পাঠানো হয়
									@elseif (session('lang_fin'))
										Vastaanota päivityksiä, kuumia tarjouksia ja alennuksia suoraan sähköpostiisi päivittäin
									@else
										Receive updates, hot deals, discounts sent straight in your inbox daily
									@endif
								</p>
								<div class="foot-news-last">
									<form action="{{route('subs.insert')}}" method="POST">
										@csrf
										<div class="form-group mb-0">
											<input type="text" class="form-control" name="email" placeholder="
												@if (session('lang_ben'))
													আপনার ইমেল
												@elseif (session('lang_fin'))
													Sähköpostiosoitteesi
												@else
													Your email
												@endif
											">
											<button type="submit" class="btn btn-dark">
												@if (session('lang_ben'))
													সাবস্ক্রাইব!
												@elseif (session('lang_fin'))
													Tilaa!
												@else
													Subscribe!
												@endif
											</button>
										</div>
									</form>
								</div>
								<div class="footer-bottom-payments">
									<div class="secure-payments">
										<span>
											@if (session('lang_ben'))
												নিরাপদ পেমেন্ট:
											@elseif (session('lang_fin'))
												Turvalliset maksut:
											@else
												Secure Payments:
											@endif
										</span>
										<div class="scr_payment"><img src="assets/img/card.png" class="img-fluid" alt="" /></div>
									</div>
								</div>
							</div>
						</div>

					</div>
				</div>
			</div>

			<div class="footer-bottom">
				<div class="container">
					<div class="row">
						<div class="col-md-12">
							<div class="copyrigths text-center">
								@if (session('lang_ben'))
									<p class="mb-0">© {{ date('Y') }} <span>IPAC</span>. সর্বসত্ত্ব সংরক্ষিত। <a href="https://osamabinabsar.github.io/">{{ isset($setting) && $setting->footer_link ? $setting->footer_link : 'Osama' }}</a> দ্বারা ডিজাইন করা হয়েছে</p>
								@elseif (session('lang_fin'))
									<p class="mb-0">© {{ date('Y') }} <span>IPAC</span>. Kaikki oikeudet pidätetään. Suunnittelija <a href="https://osamabinabsar.github.io/">{{ isset($setting) && $setting->footer_link ? $setting->footer_link : 'Osama' }}</a></p>
								@else
									<p class="mb-0">© {{ date('Y') }} <span>IPAC</span>. All rights reserved. Designed By <a href="https://osamabinabsar.github.io/">{{ isset($setting) && $setting->footer_link ? $setting->footer_link : 'Osama' }}</a></p>
								@endif
							</div>
						</div>
					</div>
				</div>
			</div>
		</footer>
		<!-- ============================ Footer End ================================== -->

		<!-- Add Wishlist -->
		<div class="w3-ch-sideBar w3-bar-block w3-card-2 w3-animate-right" style="display:none;right:0;" id="Wishlist">
			<div class="rightMenu-scroll">
				<div class="d-flex align-items-center justify-content-between slide-head py-3 px-3">
					<h4 class="cart_heading fs-md ft-medium mb-0">
						@if (session('lang_ben'))
							সঞ্চিত পণ্য
						@elseif (session('lang_fin'))
							Tallennetut Tuotteet
						@else
							Saved Products
						@endif
					</h4>
					<button onclick="closeWishlist()" class="close_slide"><i class="ti-close"></i></button>
				</div>
				<div class="right-ch-sideBar">

					<div class="cart_select_items py-2">
						<!-- Single Item -->

						@foreach (App\Models\WishTable::where('customer_id', Auth::guard('cust_login')->id())->get() as $wish)
							@if ($wish->relto_product()->exists())
								<div class="d-flex align-items-center justify-content-between br-bottom px-3 py-3">
									<div class="cart_single d-flex align-items-center">
										<div class="cart_selected_single_thumb" style="border: 1px solid rgba(0,0,0,.125)">
											<a href="{{route('product.details', $wish->relto_product->slug)}}"><img src="{{asset('uploads/product/preview/'.$wish->relto_product->preview)}}" width="60" class="img-fluid" alt="" /></a>
										</div>
										<div class="cart_single_caption pl-2">
											<h4 class="product_title fs-sm ft-medium mb-0 lh-1">{{$wish->relto_product->product_name}}</h4>

											<p class="mb-2">
												@if ($wish->color_id != null)
													<span class="text-dark small">{{$wish->relto_color->color_name}}</span>,
												@endif

												@if ($wish->size_id != null)
													<span class="text-dark small">{{$wish->relto_size->size}}</span>
												@endif
											</p>
											<h4 class="fs-sm ft-medium mb-0 lh-1">
												@if ($wish->quantity != null)
													EUR {{number_format($wish->relto_product->after_disc)}}
													* {{$wish->quantity}}
												@else
													EUR {{number_format($wish->relto_product->after_disc)}}
												@endif
											</h4>
										</div>
									</div>
									<div class="fls_last"><a href="{{route('wishlist.remove', $wish->id)}}" class="close_slide gray"><i class="ti-close"></i></a></div>
								</div>
							@else
								<div class="d-flex align-items-center justify-content-between br-bottom px-3 py-3">
									<div class="cart_single d-flex align-items-center">
										<div class="cart_selected_single_thumb" style="border: 1px solid rgba(0,0,0,.125)">
											<img src="" width="60" class="img-fluid" alt="" />
										</div>
										<div class="cart_single_caption pl-2">
											<h4 class="product_title fs-sm ft-medium mb-0 lh-1">Item Not Available Now!</h4>
										</div>
									</div>
									<div class="fls_last"><a href="{{route('wishlist.remove', $wish->id)}}" class="close_slide gray"><i class="ti-close"></i></a></div>
								</div>
							@endif
						@endforeach
					</div>

					<div class="cart_action px-3 py-3">
						<div class="form-group">
							<a href="{{Auth::guard('cust_login')->check() ?route('wishlist.remove_all') :route('customer_login')}}" style="width: 45%" class="btn btn-dark-light">
								@if (session('lang_ben'))
									সব সামগ্রী সরান
								@elseif (session('lang_fin'))
									Poista kaikki
								@else
									Remove All
								@endif
							</a>
							<a href="{{Auth::guard('cust_login')->check() ?route('customer.wishlist') :route('customer_login')}}" style="width: 45%" class="btn btn-dark-light">
							@if (session('lang_ben'))
								কার্ট দেখুন
							@elseif (session('lang_fin'))
								Katso kori
							@else
								View Whishlist
							@endif
							</a>
						</div>
						@if (session('wish_added'))
							<span class="err_msg err_msg_cart" style="visibility:visible; background: #6f42c1">
							<p>{{session('wish_added')}}</p></span>
						@endif
						@if (session('wish_removed'))
							<span class="err_msg err_msg_cart bg-warning" style="visibility:visible">
							<p>{{session('wish_removed')}}</p></span>
						@endif
					</div>

				</div>
			</div>
		</div>

		<!-- Add Card List -->
		<div class="w3-ch-sideBar w3-bar-block w3-card-2 w3-animate-right" style="display:none;right:0;" id="Cart">
			<div class="rightMenu-scroll">
				<div class="d-flex align-items-center justify-content-between slide-head py-3 px-3">
					<h4 class="cart_heading fs-md ft-medium mb-0">
					@if (session('lang_ben'))
						পণ্য তালিকা
					@elseif (session('lang_fin'))
						Tuoteluettelo
					@else
						Products List
					@endif
					</h4>
					<button onclick="closeCart()" class="close_slide"><i class="ti-close"></i></button>
				</div>
				<div class="right-ch-sideBar">
					<div class="cart_select_items py-2" style="max-height: 70vh; overflow-y:scroll">

						<!-- Single Item -->
						@php
							$total = 0;
						@endphp

						@if(Auth::guard('cust_login')->check())
							@foreach (App\Models\cartMod::where('customer_id', Auth::guard('cust_login')->id())->get() as $cart)
							<div class="d-flex align-items-center justify-content-between px-3 py-3">
								<div class="cart_single d-flex align-items-center">
									<div class="cart_selected_single_thumb" style="border: 1px solid rgba(0,0,0,.125)">
										<a href="{{route('product.details', $cart->relto_product->slug)}}"><img src="{{asset('uploads/product/preview/'.$cart->relto_product->preview)}}" width="60" class="img-fluid" alt="" /></a>
									</div>
									<div class="cart_single_caption pl-2">
										<h4 class="product_title fs-sm ft-medium mb-0 lh-1">{{$cart->relto_product->product_name}}</h4>
										<p class="mb-2"><span class="text-dark ft-medium small">{{$cart->relto_color->color_name}}</span>, <span class="text-dark small">{{$cart->relto_size->size}}</span></p>
										<h4 class="fs-sm ft-medium mb-0 lh-1">
											EUR {{number_format($cart->relto_product->after_disc)}}
											* {{$cart->quantity}}
										</h4>
									</div>
								</div>

								@if (!request()->route()->named('checkout'))
									<div class="fls_last">
										@if(Auth::guard('cust_login')->check())
											<a href="{{route('cart.remove', $cart->id)}}" class="close_slide gray"><i class="ti-close"></i></a>
										@else
											<a href="#" onclick="removeGuestCartItem('{{$cart->id}}')" class="close_slide gray"><i class="ti-close"></i></a>
										@endif
									</div>
								@endif
							</div>

							@php
								$total += $cart->relto_product->after_disc * $cart->quantity;
							@endphp
							@endforeach
						@else
							{{-- Guest user cart - simplified display --}}
							@php
								$guest_cart = session()->get('guest_cart', []);
							@endphp
							@if(count($guest_cart) > 0)
								<div class="cart_single_items">
									<div class="cart_selected_single_item">
										<div class="cart_single_item_caption text-center">
											<h4 class="product_title fs-sm ft-medium mb-2 lh-1">{{count($guest_cart)}} Items in Cart</h4>
											<p class="mb-2 text-muted">Please view cart page for details</p>
											<a href="{{route('cart.store.update')}}" class="btn btn-sm btn-dark">View Cart</a>
										</div>
									</div>
								</div>
							@endif
						@endif

					</div>

					<div class="d-flex align-items-center justify-content-between br-top br-bottom px-3 py-3">
						<h6 class="mb-0">
						@if (session('lang_ben'))
							সামগ্রীর সমষ্টি
						@elseif (session('lang_fin'))
							Ostoskorin sisältö
						@else
							Subtotal
						@endif
						</h6>

						<h3 class="mb-0 ft-medium">{{number_format($total)}} &#8364;</h3>
					</div>

					<div class="cart_action px-3 py-3">
						<div class="form-group">
							@if(Auth::guard('cust_login')->check())
								<a href="{{route('cart.remove_all')}}" style="width: 45%" class="btn btn-dark-light">
							@else
								<a href="#" onclick="clearGuestCart()" style="width: 45%" class="btn btn-dark-light">
							@endif
								@if (session('lang_ben'))
								সব সামগ্রী সরান
								@elseif (session('lang_fin'))
									Poista kaikki
								@else
									Remove All
								@endif
							</a>
							<a href="{{route('cart.store.update')}}" style="width: 45%" class="btn btn-dark-light">
							@if (session('lang_ben'))
								কার্ট দেখুন
							@elseif (session('lang_fin'))
								Katso kori
							@else
								View Cart
							@endif
							</a>
						</div>
						@if (session('cart_added'))
							<span class="err_msg err_msg_cart bg-success" style="visibility:visible">
							<p>{{session('cart_added')}}</p></span>
						@endif
						@if (session('cart_removed'))
							<span class="err_msg err_msg_cart bg-danger" style="visibility:visible">
							<p>{{session('cart_removed')}}</p></span>
						@endif
					</div>
				</div>
			</div>
		</div>

		<a id="back2Top" class="top-scroll" title="Back to top" href="#"><i class="ti-arrow-up"></i></a>


	</div>
	<!-- ============================================================== -->
	<!-- End Wrapper -->
	<!-- ============================================================== -->

	<!-- ============================================================== -->
	<!-- All Jquery -->
	<!-- ============================================================== -->
	<script src="{{asset('assets/js/jquery.min.js')}}"></script>
	<script src="{{asset('assets/js/popper.min.js')}}"></script>
	<script src="{{asset('assets/js/jquery.number.min.js')}}"></script>
	<script src="{{asset('assets/js/bootstrap.min.js')}}"></script>
	<script src="{{asset('assets/js/ion.rangeSlider.min.js')}}"></script>
	<script src="{{asset('assets/js/slick.js')}}"></script>
	<script src="{{asset('assets/js/slider-bg.js')}}"></script>
	<script src="{{asset('assets/js/lightbox.js')}}"></script>
	<script src="{{asset('assets/js/Font-Awesome.js')}}"></script>
	<script src="{{asset('assets/js/smoothproducts.js')}}"></script>
	<script src="{{asset('assets/js/snackbar.min.js')}}"></script>
	<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
	<script src="{{asset('assets/js/jQuery.style.switcher.js')}}"></script>
	<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
	<script src="{{ asset('js/share.js') }}"></script>
	<script src="{{asset('assets/js/custom.js')}}"></script>
	<script src="{{asset('assets/js/mobile-responsive.js')}}"></script>
	<script src="{{asset('assets/js/image-optimizer.js')}}"></script>
	<!-- ============================================================== -->
	<!-- This page plugins -->
	<!-- ============================================================== -->

	<script>
		function openWishlist() {
			document.getElementById("Wishlist").style.display = "block";
		}
		function closeWishlist() {
			document.getElementById("Wishlist").style.display = "none";
		}
	</script>

	<script>
		function openCart() {
			document.getElementById("Cart").style.display = "block";
		}
		function closeCart() {
			document.getElementById("Cart").style.display = "none";
		}

		// Guest cart management functions
		function removeGuestCartItem(itemKey) {
			$.ajax({
				url: '{{ route("guest.cart.remove") }}',
				method: 'POST',
				data: {
					_token: '{{ csrf_token() }}',
					item_key: itemKey
				},
				success: function(response) {
					if (response.success) {
						// Reload the page to update cart display
						location.reload();
					}
				},
				error: function() {
					alert('Error removing item from cart');
				}
			});
		}

		function clearGuestCart() {
			if (confirm('Are you sure you want to remove all items from cart?')) {
				$.ajax({
					url: '{{ route("guest.cart.clear") }}',
					method: 'POST',
					data: {
						_token: '{{ csrf_token() }}'
					},
					success: function(response) {
						if (response.success) {
							// Reload the page to update cart display
							location.reload();
						}
					},
					error: function() {
						alert('Error clearing cart');
					}
				});
			}
		}
	</script>

	<script>
		function openSearch() {
			document.getElementById("Search").style.display = "block";
		}
		function closeSearch() {
			document.getElementById("Search").style.display = "none";
		}
	</script>

	{{-- === Fixed Header === --}}
	{{-- <script>
		$(function (){
			$(window).on("scroll", function() {
				if($(window).scrollTop() > 50) {
					$(".header_one").addClass("ext_hd");
				}
				else {
					$(".header_one").removeClass("ext_hd");

				}
			});
		});
	</script> --}}

	{{-- === Subscribe Done === --}}
	@if (session('subs_done'))
		<script>
			Swal.fire({
				icon: 'success',
				title: 'Done..',
				text: '{{session('subs_done')}}',
			})
		</script>
	@endif

	{{-- === Scroll to Error === --}}
	<script>
		$('.err').show(function(){
			$(document).ready(function(){
				$("html, body").animate({
					scrollTop: $('.err').offset().top -400
				}, 1000);
			});
		})
	</script>

	{{-- === Stop Loader on Page Load === --}}
	<script>
		$(window).on('load', function () {
			$('#loader').fadeOut();
		})
	</script>

	{{-- === Custom Opened Cart === --}}
	@if (session('cart_added'))
		<script>
			document.getElementById("Cart").style.display = "block";
		</script>
	@endif
	@if (session('cart_removed'))
		<script>
			document.getElementById("Cart").style.display = "block";
		</script>
	@endif
	@if (session('wish_added'))
		<script>
			document.getElementById("Wishlist").style.display = "block";
		</script>
	@endif
	@if (session('wish_removed'))
		<script>
			document.getElementById("Wishlist").style.display = "block";
		</script>
	@endif

	{{-- === Master Search === --}}
	<script>
		$('#master_search').click(function(e){
			e.preventDefault();

			var master_inp = $('#master_inp').val() || '';
			var cate_id = $('.cate_box').val() || '';
			var subcate_id = $('input[name="subcate"]:checked').val() || '';
			var brand_id = $('input[name="brands"]:checked').val() || '';
			var min_price = $('.min_price').val() || '0';
			var max_price = $('.max_price').val() || '999999';
			var color_id = $('input[name="color"]:checked').val() || '';
			var size_id = $('input[name="size"]:checked').val() || '';
			var sort = $('.sort_box').val() || '';
			var show = $('.show_box').val() || '12';

			var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) +
				"&cate=" + cate_id +
				"&subcate=" + subcate_id +
				"&brand=" + brand_id +
				"&min=" + min_price +
				"&max=" + max_price +
				"&col=" + color_id +
				"&siz=" + size_id +
				"&sort=" + sort +
				"&show=" + show;

			window.location.href = search_link;
		});
	</script>

	{{-- ==== Script HERE ==== --}}
	@yield('footer_script')

	{{-- === Mobile Header JavaScript === --}}
	<script>
		// Mobile hamburger menu toggle
		document.addEventListener('DOMContentLoaded', function() {
			// Hamburger menu functionality
			const hamburgerMenu = document.querySelector('.hamburger-menu');
			const mobileNav = document.querySelector('.mobile-nav');
			const mobileNavOverlay = document.querySelector('.mobile-nav-overlay');
			const mobileNavClose = document.querySelector('.mobile-nav-close');

			// Force reset menu state on page load
			if (hamburgerMenu) {
				// Ensure menu starts in closed state
				hamburgerMenu.classList.remove('active');

				if (mobileNav) {
					mobileNav.classList.remove('active');
					mobileNav.style.left = '-280px';
					mobileNav.style.transform = 'translateX(-100%)';
				}

				if (mobileNavOverlay) {
					mobileNavOverlay.classList.remove('active');
					mobileNavOverlay.style.display = 'none';
				}

				document.body.style.overflow = '';

				// Fix for desktop visibility
				if (window.innerWidth >= 992) {
					hamburgerMenu.style.display = 'none';
					if (mobileNav) mobileNav.style.display = 'none';
					if (mobileNavOverlay) mobileNavOverlay.style.display = 'none';
				} else {
					// Ensure hamburger menu is visible on mobile
					hamburgerMenu.style.display = 'flex';
					hamburgerMenu.style.visibility = 'visible';
					hamburgerMenu.style.opacity = '1';
					hamburgerMenu.style.pointerEvents = 'auto';
				}

				// Hamburger menu click handler
				hamburgerMenu.addEventListener('click', function(e) {
					e.preventDefault();
					e.stopPropagation();

					// Simply toggle active class - CSS will handle the animation
					hamburgerMenu.classList.toggle('active');

					// Toggle hamburger menu visibility
					if (hamburgerMenu.classList.contains('active')) {
						hamburgerMenu.style.display = 'none';
					} else {
						hamburgerMenu.style.display = 'block';
					}


					// Toggle mobile nav
					if (mobileNav) {
						mobileNav.classList.toggle('active');

						// Explicitly set styles based on state
						if (mobileNav.classList.contains('active')) {
							mobileNav.style.left = '0';
							mobileNav.style.transform = 'translateX(0)';
						} else {
							mobileNav.style.left = '-280px';
							mobileNav.style.transform = 'translateX(-100%)';
						}
					}

					// Toggle overlay
					if (mobileNavOverlay) {
						mobileNavOverlay.classList.toggle('active');

						// Explicitly set display based on state
						if (mobileNavOverlay.classList.contains('active')) {
							mobileNavOverlay.style.display = 'none';
						} else {
							mobileNavOverlay.style.display = 'block';
						}
					}

					// Toggle body overflow based on menu state
					if (mobileNav && mobileNav.classList.contains('active')) {
						document.body.style.overflow = 'hidden';
					} else {
						document.body.style.overflow = '';
					}
				});
			}

			// Close button click handler
			if (mobileNavClose) {
				mobileNavClose.addEventListener('click', function(e) {
					e.preventDefault();
					e.stopPropagation();

					// Remove active class from hamburger
					if (hamburgerMenu) {
						hamburgerMenu.classList.remove('active');

						// Reset hamburger icon
						hamburgerMenu.querySelector('span:nth-child(1)').style.top = '12px';
						hamburgerMenu.querySelector('span:nth-child(1)').style.width = '25px';
						hamburgerMenu.querySelector('span:nth-child(1)').style.left = '8px';
						hamburgerMenu.querySelector('span:nth-child(1)').style.opacity = '1';

						hamburgerMenu.querySelector('span:nth-child(2)').style.transform = 'rotate(0deg)';
						hamburgerMenu.querySelector('span:nth-child(3)').style.transform = 'rotate(0deg)';

						hamburgerMenu.querySelector('span:nth-child(4)').style.top = '26px';
						hamburgerMenu.querySelector('span:nth-child(4)').style.width = '25px';
						hamburgerMenu.querySelector('span:nth-child(4)').style.left = '8px';
						hamburgerMenu.querySelector('span:nth-child(4)').style.opacity = '1';
					}

					// Hide mobile nav
					if (mobileNav) {
						mobileNav.classList.remove('active');
						mobileNav.style.left = '-280px';
						mobileNav.style.transform = 'translateX(-100%)';
					}

					// Hide overlay
					if (mobileNavOverlay) {
						mobileNavOverlay.classList.remove('active');
						mobileNavOverlay.style.display = 'none';
					}

					document.body.style.overflow = '';
				});
			}

			// Overlay click handler
			if (mobileNavOverlay) {
				mobileNavOverlay.addEventListener('click', function(e) {
					e.preventDefault();
					e.stopPropagation();

					// Remove active class from hamburger - CSS will handle the animation
					if (hamburgerMenu) {
						hamburgerMenu.classList.remove('active');
					}

					// Hide mobile nav
					if (mobileNav) {
						mobileNav.classList.remove('active');
						mobileNav.style.left = '-280px';
						mobileNav.style.transform = 'translateX(-100%)';
					}

					// Hide overlay
					mobileNavOverlay.classList.remove('active');
					mobileNavOverlay.style.display = 'none';

					document.body.style.overflow = '';
				});
			}

			// Mobile search functionality
			const mobileSearchBtn = document.querySelector('.mobile-search-btn');
			const mobileSearchForm = document.querySelector('.mobile-search-form');

			if (mobileSearchBtn && mobileSearchForm) {
				mobileSearchBtn.addEventListener('click', function() {
					mobileSearchForm.classList.toggle('active');
				});

				// Close search form when clicking outside
				document.addEventListener('click', function(event) {
					if (!mobileSearchBtn.contains(event.target) && !mobileSearchForm.contains(event.target)) {
						mobileSearchForm.classList.remove('active');
					}
				});
			}

			// Handle window resize to ensure proper visibility
			window.addEventListener('resize', function() {
				if (window.innerWidth >= 992) {
					// On desktop
					if (hamburgerMenu) hamburgerMenu.style.display = 'none';
					if (mobileNav) {
						mobileNav.classList.remove('active');
						mobileNav.style.display = 'none';
					}
					if (mobileNavOverlay) {
						mobileNavOverlay.classList.remove('active');
						mobileNavOverlay.style.display = 'none';
					}
					document.body.style.overflow = '';
				} else {
					// On mobile
					if (hamburgerMenu) hamburgerMenu.style.display = 'flex';
					// Don't automatically show the menu on resize, just make it available
				}
			});

			// Close menu when clicking anywhere else on the screen
			document.addEventListener('click', function(event) {
				// Check if menu is open and click is outside the menu and hamburger button
				if (mobileNav && mobileNav.classList.contains('active') &&
					!mobileNav.contains(event.target) &&
					!hamburgerMenu.contains(event.target)) {

					// Close the menu
					if (hamburgerMenu) hamburgerMenu.classList.remove('active');
					if (mobileNav) {
						mobileNav.classList.remove('active');
						mobileNav.style.left = '-280px';
						mobileNav.style.transform = 'translateX(-100%)';
					}
					if (mobileNavOverlay) {
						mobileNavOverlay.classList.remove('active');
						mobileNavOverlay.style.display = 'none';
					}
					document.body.style.overflow = '';
				}

				// Close cart when clicking outside
				const cartSidebar = document.getElementById('Cart');
				const cartTriggers = document.querySelectorAll('[onclick*="openCart"]');
				let clickedCartTrigger = false;

				cartTriggers.forEach(trigger => {
					if (trigger.contains(event.target)) {
						clickedCartTrigger = true;
					}
				});

				if (cartSidebar && cartSidebar.style.display === 'block' &&
					!cartSidebar.contains(event.target) && !clickedCartTrigger) {
					closeCart();
				}

				// Close wishlist when clicking outside
				const wishlistSidebar = document.getElementById('Wishlist');
				const wishlistTriggers = document.querySelectorAll('[onclick*="openWishlist"]');
				let clickedWishlistTrigger = false;

				wishlistTriggers.forEach(trigger => {
					if (trigger.contains(event.target)) {
						clickedWishlistTrigger = true;
					}
				});

				if (wishlistSidebar && wishlistSidebar.style.display === 'block' &&
					!wishlistSidebar.contains(event.target) && !clickedWishlistTrigger) {
					closeWishlist();
				}
			});
		});
	</script>

	<!-- ======================= Mobile Application ======================== -->
	<section class="gray py-5 position-relative">
		<div class="container">
			<div class="row justify-content-center">
				<div class="col-lg-7 col-md-10 text-center">
					<div class="sec_title position-relative text-center mb-5">
						<h2 class="ft-bold">
							@if (session('lang_ben'))
								আমাদের অ্যাপ ডাউনলোড করুন
							@elseif (session('lang_fin'))
								Lataa sovelluksemme
							@else
								Download Our App
							@endif
						</h2>
						<div class="sec_title_para theme-cl-2 mb-0 ps-0">
							@if (session('lang_ben'))
								বাজারে সবচেয়ে ভাল শপিং অভিজ্ঞতার জন্য
							@elseif (session('lang_fin'))
								Parhaan ostoskokemuksen saamiseksi
							@else
								For Better Shopping Experience
							@endif
						</div>
					</div>
				</div>
			</div>

			<div class="row justify-content-center">
				<div class="col-xl-10 col-lg-11 col-md-12">
					<div class="row justify-content-center">
						<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12">
							<div class="app_btn">
								<a href="#" class="app_single_link">
									<div class="d-flex align-items-center">
										<div class="app_icon">
											<i class="fas fa-apple"></i>
										</div>
										<div class="app_title">
											<div class="app_title_first">
												@if (session('lang_ben'))
													পেতে
												@elseif (session('lang_fin'))
													Lataa
												@else
													Get it on
												@endif
											</div>
											<h4 class="app_title_last">App Store</h4>
										</div>
									</div>
								</a>
							</div>
						</div>
						<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12">
							<div class="app_btn">
								<a href="#" class="app_single_link">
									<div class="d-flex align-items-center">
										<div class="app_icon">
											<i class="fas fa-play"></i>
										</div>
										<div class="app_title">
											<div class="app_title_first">
												@if (session('lang_ben'))
													পেতে
												@elseif (session('lang_fin'))
													Lataa
												@else
													Get it on
												@endif
											</div>
											<h4 class="app_title_last">Google Play</h4>
										</div>
									</div>
								</a>
							</div>
						</div>
						<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12">
							<div class="app_btn">
								<a href="#" class="app_single_link">
									<div class="d-flex align-items-center">
										<div class="app_icon">
											<i class="fas fa-windows"></i>
										</div>
										<div class="app_title">
											<div class="app_title_first">
												@if (session('lang_ben'))
													পেতে
												@elseif (session('lang_fin'))
													Lataa
												@else
													Get it on
												@endif
											</div>
											<h4 class="app_title_last">Windows Store</h4>
										</div>
									</div>
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- ======================= Mobile Application ======================== -->

<!-- Service Worker Registration for PWA -->
<!-- <script>
	if ('serviceWorker' in navigator) {
		window.addEventListener('load', function() {
			navigator.serviceWorker.register('/service-worker.js').then(function(registration) {
				console.log('ServiceWorker registration successful with scope: ', registration.scope);
			}, function(err) {
				console.log('ServiceWorker registration failed: ', err);
			});
		});
	}
</script> -->
</body>

</html>