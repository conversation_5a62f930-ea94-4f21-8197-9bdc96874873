@extends('layouts.master')



@section('header_css')
<style>
    /* Modern Shop Page Styling */
    :root {
        --primary-color: #4CAF50;
        --primary-hover: #03A9F4;
        --highlight-color: #FFEB3B;
        --text-color: #333;
        --light-gray: #f8f9fa;
        --medium-gray: #e9ecef;
        --dark-gray: #495057;
        --border-radius: 8px;
        --card-shadow: 0 2px 10px rgba(0,0,0,0.05);
        --hover-shadow: 0 5px 15px rgba(0,0,0,0.1);
        --transition: all 0.3s ease;
    }

    /* General Styling */
    .shop-container {
        font-family: 'Inter', sans-serif;
    }

    /* Banner Section */
    .shop-banner {
        position: relative;
        overflow: hidden;
        border-radius: var(--border-radius);
        margin-bottom: 2rem;
    }

    .shop-banner::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to right, rgba(0,0,0,0.4), rgba(0,0,0,0.1));
        z-index: 1;
    }

    .shop-banner .text-left {
        position: relative;
        z-index: 2;
    }

    .shop-banner h1 {
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        color: white;
    }

    /* Breadcrumb */
    .shop-breadcrumb {
        background-color: var(--light-gray);
        border-radius: var(--border-radius);
        padding: 0.75rem 1rem;
        margin-bottom: 2rem;
    }

    .shop-breadcrumb .breadcrumb {
        margin-bottom: 0;
        padding: 0;
    }

    /* Section Title */
    .shop-title {
        margin-bottom: 2rem;
        text-align: center;
    }

    .shop-title h2 {
        color: var(--dark-gray);
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 2px;
        margin-bottom: 0.5rem;
    }

    .shop-title h3 {
        font-weight: 700;
        font-size: 2rem;
        color: var(--text-color);
    }

    /* Filter Sidebar */
    .filter-sidebar {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .filter-section {
        border-bottom: 1px solid var(--medium-gray);
        padding: 1.5rem;
    }

    .filter-section:last-child {
        border-bottom: none;
    }

    .filter-header {
        cursor: pointer;
        margin-bottom: 1rem;
    }

    .filter-header h4 {
        font-weight: 600;
        font-size: 1rem;
        margin: 0;
        color: var(--text-color);
    }

    .filter-header h4 a {
        color: var(--text-color);
        text-decoration: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .filter-header h4 a::after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        transition: var(--transition);
    }

    .filter-header h4 a[aria-expanded="true"]::after {
        transform: rotate(180deg);
    }

    .filter-body {
        padding-top: 0.5rem;
    }

    /* Form Controls */
    .filter-sidebar .form-control {
        border-radius: var(--border-radius);
        border: 1px solid var(--medium-gray);
        padding: 0.75rem;
    }

    .filter-sidebar .btn {
        background-color: var(--primary-color);
        border: none;
        color: white;
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: var(--transition);
    }

    .filter-sidebar .btn:hover {
        background-color: var(--primary-hover);
    }

    .filter-sidebar .custom-select {
        border-radius: var(--border-radius);
        border: 1px solid var(--medium-gray);
        padding: 0.75rem;
        height: auto;
        background-position: right 0.75rem center;
    }

    /* Color and Size Options */
    .filter-sidebar .form-check-input {
        margin-top: 0.25rem;
    }

    .filter-sidebar .form-option-label {
        height: auto;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        border: 1px solid var(--medium-gray);
        transition: var(--transition);
    }

    .filter-sidebar .form-option-label:hover {
        border-color: var(--primary-color);
    }

    .filter-sidebar .form-check-input:checked + .form-option-label {
        border-color: var(--primary-color);
        background-color: rgba(76, 175, 80, 0.1);
    }

    .filter-sidebar .form-option-color {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: inline-block;
    }

    /* Product Grid */
    .product-grid {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .product-grid:hover {
        box-shadow: var(--hover-shadow);
        transform: translateY(-5px);
    }

    .product-thumb {
        position: relative;
        padding-top: 100%;
        overflow: hidden;
    }

    .product-thumb img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
        transition: var(--transition);
    }

    .product-grid:hover .product-thumb img {
        transform: scale(1.05);
    }

    .product-info {
        padding: 1.5rem;
    }

    .product-category {
        font-size: 0.75rem;
        color: var(--dark-gray);
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 0.5rem;
    }

    .product-title {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.5rem;
        line-height: 1.4;
        height: 2.8em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .product-title a {
        color: var(--text-color);
        text-decoration: none;
    }

    .product-rating {
        margin-bottom: 0.5rem;
    }

    .product-rating .fas.fa-star.filled {
        color: #FF9800;
    }

    .product-price {
        font-weight: 700;
        font-size: 1.25rem;
        color: var(--primary-color);
    }

    .product-price .old-price {
        text-decoration: line-through;
        color: var(--dark-gray);
        font-weight: 400;
        font-size: 0.875rem;
        margin-right: 0.5rem;
    }

    /* Badges */
    .product-badge {
        position: absolute;
        top: 1rem;
        z-index: 10;
        padding: 0.25rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        border-radius: 50px;
    }

    .badge-left {
        left: 1rem;
    }

    .badge-right {
        right: 1rem;
    }

    .badge-sale {
        background-color: var(--highlight-color);
        color: var(--text-color);
    }

    .badge-discount {
        background-color: #F44336;
        color: white;
    }

    .badge-new {
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        border: 1px solid white;
    }

    .badge-out {
        background-color: rgba(255, 0, 0, 0.8);
        color: white;
    }

    /* Wishlist Button */
    .wishlist-btn {
        position: absolute;
        top: 1rem;
        left: 1rem;
        z-index: 10;
        width: 35px;
        height: 35px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        cursor: pointer;
        transition: var(--transition);
    }

    .wishlist-btn:hover {
        background-color: white;
        transform: scale(1.1);
    }

    .wishlist-btn i {
        color: #ccc;
        transition: var(--transition);
    }

    .wishlist-btn i.clicked,
    .wishlist-btn:hover i {
        color: #F44336;
    }

    /* Search and Sort Area */
    .shop-filters {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .shop-filters .form-control,
    .shop-filters .custom-select {
        border-radius: var(--border-radius);
        border: 1px solid var(--medium-gray);
        height: auto;
        padding: 0.75rem;
    }

    .shop-filters .btn {
        background-color: var(--primary-color);
        border: none;
        color: white;
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: var(--transition);
    }

    .shop-filters .btn:hover {
        background-color: var(--primary-hover);
    }

    /* Pagination */
    .shop-pagination {
        margin-top: 2rem;
    }

    .shop-pagination .pagination {
        justify-content: center;
    }

    .shop-pagination .page-item .page-link {
        border-radius: var(--border-radius);
        margin: 0 0.25rem;
        color: var(--text-color);
        border: 1px solid var(--medium-gray);
        transition: var(--transition);
    }

    .shop-pagination .page-item.active .page-link,
    .shop-pagination .page-item .page-link:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    /* No Products Found */
    .no-products {
        text-align: center;
        padding: 3rem 0;
    }

    .no-products img {
        max-width: 200px;
        margin-bottom: 1.5rem;
    }

    .no-products h4 {
        font-weight: 500;
        color: var(--dark-gray);
    }

    /* Quick Add to Cart Styling */
    .quick-add-section {
        border-top: 1px solid var(--medium-gray);
        padding-top: 1rem;
    }

    .quick-add-form .quick-select-group select {
        border-radius: var(--border-radius);
        border: 1px solid var(--medium-gray);
        font-size: 0.875rem;
        height: 32px;
        transition: var(--transition);
    }

    .quick-add-form .quick-select-group select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
    }

    .quantity-selector select {
        width: 60px;
        border-radius: var(--border-radius);
        border: 1px solid var(--medium-gray);
        font-size: 0.875rem;
        height: 32px;
        text-align: center;
    }

    .quick-add-btn {
        background-color: var(--primary-color);
        border: none;
        color: white;
        border-radius: var(--border-radius);
        font-weight: 600;
        font-size: 0.875rem;
        height: 32px;
        transition: var(--transition);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quick-add-btn:hover {
        background-color: var(--primary-hover);
        transform: translateY(-1px);
    }

    .quick-add-btn:disabled {
        background-color: var(--medium-gray);
        color: var(--dark-gray);
        cursor: not-allowed;
    }

    .quick-add-btn i {
        font-size: 0.75rem;
    }

    /* Loading state for quick add */
    .quick-add-btn.loading {
        position: relative;
        color: transparent;
    }

    .quick-add-btn.loading::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Success state */
    .quick-add-btn.success {
        background-color: #28a745;
    }

    /* Responsive Adjustments */
    @media (max-width: 991.98px) {
        .shop-title h3 {
            font-size: 1.5rem;
        }

        .product-info {
            padding: 1rem;
        }

        .container.shop-container {
            padding: 1rem;
        }
    }

    @media (max-width: 767.98px) {
        .shop-filters {
            padding: 0.75rem;
        }

        .shop-filters .row > div {
            margin-bottom: 0.75rem;
        }

        .product-grid {
            margin-bottom: 1.5rem;
        }

        .quick-add-form .d-flex {
            flex-direction: column;
        }

        .quantity-selector {
            margin-right: 0 !important;
            margin-bottom: 0.5rem;
        }

        .quantity-selector select {
            width: 100%;
        }

        .quick-add-btn {
            width: 100%;
        }
    }

    /* Original CSS (modified) */
    .size_part .form-option-label {
        height: auto;
    }

    .clear_box {
        justify-content: end;
    }

    .filter_wraps {
        justify-content: end;
    }

    .filter_wraps a {
        height: 37px !important;
        width: 100px;
    }

    @media(min-width: 768px) and (max-width: 991.98px){
        .filter_wraps {
            justify-content: start;
        }
    }

    @media (max-width: 1199.98px){
        .clear_box {
            justify-content: start;
            margin-top: 5px;
            margin-bottom: 5px !important;
        }
    }
</style>
@endsection



@section('content')
<!-- ======================= Shop Banner ======================== -->
<!-- <section class="shop-banner bg-cover" style="background:url({{asset('assets/img/')}}) no-repeat center/cover;">
    <div class="container">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="text-left py-5 mt-3 mb-3">
                    <h1 class="ft-medium mb-3" id={{@$_GET['chk'] ?'crump' :''}}>
                        @if (session('lang_ben'))
                            ইপাক টেলিকম
                        @elseif (session('lang_fin'))
                            Ipac Telecom Verkkokauppa
                        @else
                            Ipac Telecom Online Shop
                        @endif
                    </h1>
                </div>
            </div>
        </div>
    </div>
</section> -->
<!-- ======================= Shop Banner End ======================== -->

<!-- ======================= Breadcrumb ======================== -->
<section class="py-3">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="shop-breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{route('home_page')}}">
                                @if (session('lang_ben'))
                                    হোম
                                @elseif (session('lang_fin'))
                                    Koti
                                @else
                                    Home
                                @endif
                            </a></li>
                            <li class="breadcrumb-item active" aria-current="page">
                                @if (session('lang_ben'))
                                    দোকান পৃষ্ঠা
                                @elseif (session('lang_fin'))
                                    Verkkokauppa
                                @else
                                    Online Store
                                @endif
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ======================= Breadcrumb End ======================== -->




<!-- ======================= All Product List ======================== -->
<section class="middle">
    <div class="container shop-container">

        <!-- <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="shop-title">
                 <h3>
                        @if (session('lang_ben'))
                            ইপাক স্টোর
                        @elseif (session('lang_fin'))
                            Ipac Verkkokauppa
                        @else
                            Ipac Online Store
                        @endif
                    </h3> -->
                    <!-- <h2>
                        @if (session('lang_ben'))
                            দোকানের পণ্য
                        @elseif (session('lang_fin'))
                            Kaupan tuotteet
                        @else
                            Shop Items
                        @endif
                    </h2>
                    <h3>
                        @if (session('lang_ben'))
                            ইপাক স্টোর
                        @elseif (session('lang_fin'))
                            Ipac Kauppa
                        @else
                            Ipac Store
                        @endif
                    </h3> 
                </div>
            </div>
        </div> -->

        <div class="row">

            <div class="col-xl-3 col-lg-4 col-md-12 col-sm-12">
                <div class="filter-sidebar">
                    <div class="filter-sidebar-body">

                        <!-- Price -->
                        <div class="filter-section">
                            <div class="filter-header">
                                <h4><a href="#pricing" data-toggle="collapse" aria-expanded="false" role="button">
                                    @if (session('lang_ben'))
                                        মূল্য
                                    @elseif (session('lang_fin'))
                                        Hinnoittelu
                                    @else
                                        Pricing
                                    @endif
                                </a></h4>
                            </div>
                            <div class="filter-body collapse show" id="pricing" data-parent="#pricing">
                                <div class="row">
                                    <div class="col-lg-6 col-6 pr-1">
                                        <div class="form-group">
                                            <input type="number" class="form-control min_price" placeholder="Min" value="{{@$_GET['min'] ?$_GET['min'] :''}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-6 pl-1">
                                        <div class="form-group">
                                            <input type="number" class="form-control max_price" placeholder="Max" value="{{@$_GET['max'] ?$_GET['max'] :''}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-block price_box">
                                                @if (session('lang_ben'))
                                                    জমা দিন
                                                @elseif (session('lang_fin'))
                                                    Lähetä
                                                @else
                                                    Submit
                                                @endif
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Categories -->
                        <div class="filter-section">
                            <div class="filter-header">
                                <h4><a href="#Categories" data-toggle="collapse" aria-expanded="false" role="button">
                                    @if (session('lang_ben'))
                                        ক্যাটাগরি
                                    @elseif (session('lang_fin'))
                                        Kategoriat
                                    @else
                                        Categories
                                    @endif
                                </a></h4>
                            </div>
                            <div class="filter-body collapse show" id="Categories" data-parent="#Categories">
                                <div class="single_filter_card">
                                    <div class="inner_widget_link">
                                        <select class="custom-select cate_box" name="cate">
                                            <option value=""> --
                                                @if (session('lang_ben'))
                                                    নির্বাচন করুন
                                                @elseif (session('lang_fin'))
                                                    Valitse
                                                @else
                                                    Select
                                                @endif
                                             -- </option>
                                            @foreach ($cate_all as $cate)
                                                <option {{@$_GET['cate'] == $cate->id ?'selected' :''}} value="{{$cate->id}}">
                                                    @if (session('lang_ben'))
                                                        {{ $cate->cata_name_ben ?? $cate->cata_name }}
                                                    @elseif (session('lang_fin'))
                                                        {{ $cate->cata_name_fin ?? $cate->cata_name }}
                                                    @else
                                                        {{ $cate->cata_name }}
                                                    @endif
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sub-Categories -->
                        <div class="filter-section">
                            <div class="filter-header">
                                <h4><a href="#Subcategories" data-toggle="collapse" aria-expanded="false" role="button">
                                    @if (session('lang_ben'))
                                        উপ-ক্যাটাগরি
                                    @elseif (session('lang_fin'))
                                        Alakategoriat
                                    @else
                                        Sub-Categories
                                    @endif
                                </a></h4>
                            </div>
                            <div class="filter-body collapse show" id="Subcategories" data-parent="#subcategories">
                                <div class="single_filter_card">
                                    <div class="inner_widget_link">
                                        @if (@$_GET['cate'] && @$_GET['cate'] != '' && @$_GET['cate'] != 'undefined')
                                            <ul class="no-ul-list">
                                                @foreach ($subcate_all->where('cata_id', @$_GET['cate']) as $subcate)
                                                    @php
                                                        $subcate_products = App\models\Product_list::where('subcata_id', $subcate->id)->count();
                                                    @endphp

                                                    <li>
                                                        <input {{@$_GET['subcate'] == $subcate->id ?'checked' :''}} id="subcate{{$subcate->id}}" class="checkbox-custom subcate_box" name="subcate" type="radio" value="{{$subcate->id}}">
                                                        <label for="subcate{{$subcate->id}}" class="checkbox-custom-label">
                                                            @if (session('lang_ben'))
                                                                {{ $subcate->sub_cata_name_ben ?? $subcate->sub_cata_name }}
                                                            @elseif (session('lang_fin'))
                                                                {{ $subcate->sub_cata_name_fin ?? $subcate->sub_cata_name }}
                                                            @else
                                                                {{ $subcate->sub_cata_name }}
                                                            @endif
                                                            <span>{{$subcate_products}}</span>
                                                        </label>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <select class="custom-select subcate_dropdown" name="subcate">
                                                <option>
                                                    @if (session('lang_ben'))
                                                        -- -- ক্যাটাগরি নির্বাচন করুন -- --
                                                    @elseif (session('lang_fin'))
                                                        -- -- Valitse kategoria -- --
                                                    @else
                                                        -- -- Select Category -- --
                                                    @endif
                                                </option>
                                            </select>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Colors -->
                        <div class="filter-section">
                            <div class="filter-header">
                                <h4><a href="#colors" data-toggle="collapse" class="{{@$_GET['subcate'] && @$_GET['subcate'] != '' && @$_GET['subcate'] != 'undefined' || @$_GET['col'] && @$_GET['col'] != '' && @$_GET['col'] != 'undefined' ?'' :'collapsed'}}" aria-expanded="false" role="button">
                                    @if (session('lang_ben'))
                                        রং
                                    @elseif (session('lang_fin'))
                                        Värit
                                    @else
                                        Colors
                                    @endif
                                </a></h4>
                            </div>
                            <div class="filter-body collapse {{@$_GET['subcate'] && @$_GET['subcate'] != '' && @$_GET['subcate'] != 'undefined' || @$_GET['col'] && @$_GET['col'] != '' && @$_GET['col'] != 'undefined' ?'show' :''}}" id="colors" data-parent="#colors">
                                <div class="single_filter_card">
                                    <div class="text-left">
                                        @if (@$_GET['subcate'] && @$_GET['subcate'] != '' && @$_GET['subcate'] != 'undefined')
                                            @php
                                                $store = '';
                                            @endphp

                                            @foreach ($color_all as $sl=>$color)
                                                @php
                                                    $cate_products = App\Models\Product_list::where('subcata_id', @$_GET['subcate'])->get();
                                                @endphp

                                                @foreach ($cate_products as $key=>$item)
                                                    @php
                                                        $color_check = App\Models\Inventory::where('product_id', $item->id)->where('color', $color->id);
                                                    @endphp

                                                    @if ($color_check->first() && $store != $color->id)
                                                        <div class="form-check form-option form-check-inline mb-1">
                                                            <input {{@$_GET['col'] == $color->id ?'checked' :''}}
                                                            class="form-check-input color_box" type="radio" name="color" id="whitea{{$color->id}}" value="{{$color->id}}">
                                                            <label class="form-option-label rounded-circle" for="whitea{{$color->id}}" title="{{$color->color_name}}"><span class="form-option-color rounded-circle" style="background: {{$color->color_code}}"></span></label>
                                                        </div>

                                                        @php
                                                            $store = $color->id;
                                                        @endphp
                                                    @endif
                                                @endforeach
                                            @endforeach
                                        @else
                                            @foreach ($color_all as $color)
                                                <div class="form-check form-option form-check-inline mb-1">
                                                    <input {{@$_GET['col'] == $color->id ?'checked' :''}}
                                                    class="form-check-input color_box" type="radio" name="color" id="whitea{{$color->id}}" value="{{$color->id}}">
                                                    <label class="form-option-label rounded-circle" for="whitea{{$color->id}}" title="{{$color->color_name}}"><span class="form-option-color rounded-circle" style="background: {{$color->color_code}}"></span></label>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sizes -->
                        <div class="filter-section">
                            <div class="filter-header">
                                <h4><a href="#size" data-toggle="collapse" class="{{(@$_GET['subcate'] && @$_GET['subcate'] != '' && @$_GET['subcate'] != 'undefined') || (@$_GET['siz'] && @$_GET['siz'] != '' && @$_GET['siz'] != 'undefined') ?'' :'collapsed'}}" aria-expanded="false" role="button">
                                    @if (session('lang_ben'))
                                        সাইজ
                                    @elseif (session('lang_fin'))
                                        Koko
                                    @else
                                        Size
                                    @endif
                                </a></h4>
                            </div>
                            <div class="filter-body collapse {{@$_GET['subcate'] && @$_GET['subcate'] != '' && @$_GET['subcate'] != 'undefined' || @$_GET['siz'] && @$_GET['siz'] != '' && @$_GET['siz'] != 'undefined' ?'show' :''}}" id="size" data-parent="#size">
                                <div class="single_filter_card">
                                    <div class="text-left">
                                        @if (@$_GET['subcate'] && @$_GET['subcate'] != '' && @$_GET['subcate'] != 'undefined')
                                            @php
                                                $store = '';
                                            @endphp

                                            @foreach ($size_all as $sl=>$size)
                                                @php
                                                    $cate_products = App\Models\Product_list::where('subcata_id', @$_GET['subcate'])->get();
                                                @endphp

                                                @foreach ($cate_products as $key=>$item)
                                                    @php
                                                        $size_check = App\Models\Inventory::where('product_id', $item->id)->where('size', $size->id);
                                                    @endphp

                                                    @if ($size_check->first() && $store != $size->id)
                                                        <div class="form-check form-option form-check-inline mb-2">
                                                            <input {{@$_GET['siz'] == $size->id ?'checked' :''}}
                                                            class="form-check-input size_box" type="radio" name="size" id="siz{{$size->id}}" value="{{$size->id}}">
                                                            <label class="form-option-label" for="siz{{$size->id}}">{{$size->size}}</label>
                                                        </div>

                                                        @php
                                                            $store = $size->id;
                                                        @endphp
                                                    @endif
                                                @endforeach
                                            @endforeach
                                        @else
                                            @foreach ($size_type as $type)
                                                @if ($type->size_type == 'General')
                                                    @php
                                                        $size_items = App\models\Size::where('size_type', $type->size_type)->get()
                                                    @endphp
                                                @else
                                                    @php
                                                        $size_items = App\models\Size::where('size_type', $type->size_type)->orderBy('size')->get()
                                                    @endphp
                                                @endif

                                                @foreach ($size_items as $size)
                                                    @php
                                                        $size_avail = App\Models\Inventory::where('size', $size->id)
                                                    @endphp

                                                    @if ($size_avail->exists())
                                                        <div class="form-check form-option form-check-inline mb-2">
                                                            <input {{@$_GET['siz'] == $size->id ?'checked' :''}}
                                                            class="form-check-input size_box" type="radio" name="size" id="siz{{$size->id}}" value="{{$size->id}}">
                                                            <label class="form-option-label" for="siz{{$size->id}}">{{$size->size}}</label>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            @endforeach
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Brands Option -->
                        <div class="filter-section">
                            <div class="filter-header">
                                <h4><a href="#brands" data-toggle="collapse" aria-expanded="false" role="button">
                                    @if (session('lang_ben'))
                                        ব্র্যান্ড
                                    @elseif (session('lang_fin'))
                                        Brändit
                                    @else
                                        Brands
                                    @endif
                                </a></h4>
                            </div>
                            <div class="filter-body collapse show" id="brands" data-parent="#brands">
                                <div class="single_filter_card">
                                    <div class="inner_widget_link">
                                        <ul class="no-ul-list">
                                            @foreach ($brand_all as $brand)
                                                @php
                                                    $brand_products = App\models\Product_list::where('brand', $brand->brand)->count();
                                                @endphp

                                                <li>
                                                    <input {{@$_GET['brand'] == $brand->brand ?'checked' :''}}
                                                    id="brand{{$brand->id}}" class="checkbox-custom brand_box" name="brands" type="radio" value="{{$brand->brand}}">
                                                    <label for="brand{{$brand->id}}" class="checkbox-custom-label">{{$brand->brand}}<span>{{$brand_products}}</span></label>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="col-xl-9 col-lg-8 col-md-12 col-sm-12">

                {{-- === Search Top === --}}
                <div class="shop-filters mb-4">
                    <div class="row align-items-center">
                        <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 mb-3 mb-md-0">
                            <h6 class="mb-0 font-weight-normal">
                                @if (session('lang_ben'))
                                    পাওয়া গেছে: <span class="font-weight-bold">{{$store_items->total()}}</span>
                                @elseif (session('lang_fin'))
                                    Tuotteita löydetty: <span class="font-weight-bold">{{$store_items->total()}}</span>
                                @else
                                    Items Found: <span class="font-weight-bold">{{$store_items->total()}}</span>
                                @endif
                            </h6>
                        </div>
                        <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 mb-3 mb-md-0">
                            <div class="d-flex align-items-center">
                                <select class="custom-select show_box" name="show">
                                    <option {{@$_GET['show'] == 1 ?'selected' :''}} value="1">
                                        @if (session('lang_ben'))
                                            দেখানো হচ্ছে ( ৯ )
                                        @elseif (session('lang_fin'))
                                            Näytetään ( 9 )
                                        @else
                                            Showing ( 9 )
                                        @endif
                                    </option>
                                    <option {{@$_GET['show'] == 2 ?'selected' :''}} value="2">
                                        @if (session('lang_ben'))
                                            দেখানো হচ্ছে ( ২০ )
                                        @elseif (session('lang_fin'))
                                            Näytetään ( 20 )
                                        @else
                                            Showing ( 20 )
                                        @endif
                                    </option>
                                    <option {{@$_GET['show'] == 3 ?'selected' :''}} value="3">
                                        @if (session('lang_ben'))
                                            দেখানো হচ্ছে ( ৫০ )
                                        @elseif (session('lang_fin'))
                                            Näytetään ( 50 )
                                        @else
                                            Showing ( 50 )
                                        @endif
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-8 col-sm-12 mb-3 mb-md-0">
                            <div class="d-flex align-items-center">
                                <select class="custom-select sort_box">
                                    <option {{@$_GET['sort'] == 1 ?'selected' :''}} value="1">
                                        @if (session('lang_ben'))
                                            ডিফল্ট সাজানো (সর্বশেষ)
                                        @elseif (session('lang_fin'))
                                            Oletuslajittelu (Uusin)
                                        @else
                                            Default Sorting (Latest)
                                        @endif
                                    </option>
                                    <option {{@$_GET['sort'] == 2 ?'selected' :''}} value="2">
                                        @if (session('lang_ben'))
                                            নাম অনুসারে সাজান: A-Z
                                        @elseif (session('lang_fin'))
                                            Järjestä nimen mukaan: A-Z
                                        @else
                                            Sort by Name: A-Z
                                        @endif
                                    </option>
                                    <option {{@$_GET['sort'] == 3 ?'selected' :''}} value="3">
                                        @if (session('lang_ben'))
                                            নাম অনুসারে সাজান: Z-A
                                        @elseif (session('lang_fin'))
                                            Järjestä nimen mukaan: Z-A
                                        @else
                                            Sort by Name: Z-A
                                        @endif
                                    </option>
                                    <option {{@$_GET['sort'] == 4 ?'selected' :''}} value="4">
                                        @if (session('lang_ben'))
                                            মূল্য অনুসারে সাজান: কম মূল্য
                                        @elseif (session('lang_fin'))
                                            Järjestä hinnan mukaan: Edullisin
                                        @else
                                            Sort by price: Low price
                                        @endif
                                    </option>
                                    <option {{@$_GET['sort'] == 5 ?'selected' :''}} value="5">
                                        @if (session('lang_ben'))
                                            মূল্য অনুসারে সাজান: বেশি মূল্য
                                        @elseif (session('lang_fin'))
                                            Järjestä hinnan mukaan: Korkein hinta
                                        @else
                                            Sort by price: High price
                                        @endif
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-xl-2 col-lg-2 col-md-4 col-sm-12">
                            <div class="d-flex justify-content-end">
                                <a href="{{route('shop_page')}}" class="btn btn-block">
                                    @if (session('lang_ben'))
                                        পরিষ্কার
                                    @elseif (session('lang_fin'))
                                        Tyhjennä
                                    @else
                                        Clear
                                    @endif
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- === Product Items === --}}
                <div class="row">
                    @forelse ($store_items as $product)
                        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12 mb-4">
                            <div class="product-grid">
                                {{-- Sale Badge --}}
                                @if ($product->discount != 0)
                                    <div class="product-badge badge-sale badge-left">
                                        @if (session('lang_ben'))
                                            বিক্রয়
                                        @elseif (session('lang_fin'))
                                            Ale
                                        @else
                                            Sale
                                        @endif
                                    </div>
                                    <div class="product-badge badge-discount badge-right">-{{$product->discount}}%</div>
                                @endif

                                {{-- New Arrival Badge --}}
                                @foreach (App\Models\Inventory::where('product_id', $product->id)->get() as $inv_upd)
                                    @if($inv_upd->created_at != null && Carbon\carbon::now()->diffInDays($inv_upd->created_at) < 30)
                                        <div class="product-badge badge-new badge-right" style="top: 4rem;">
                                            @if (session('lang_ben'))
                                                নতুন আগমন!
                                            @elseif (session('lang_fin'))
                                                Uusi tuote!
                                            @else
                                                New Arrival!
                                            @endif
                                        </div>
                                    @endif
                                @endforeach

                                {{-- Out of Stock Badge --}}
                                @php
                                    $total_qty = 0;
                                @endphp

                                @foreach (App\Models\Inventory::where('product_id', $product->id)->get() as $inv_upd)
                                    @php
                                        $total_qty += $inv_upd->quantity;
                                    @endphp
                                @endforeach

                                @if($total_qty == 0)
                                    <div class="product-badge badge-out badge-right" style="top: 4rem;">
                                        @if (session('lang_ben'))
                                            স্টক শেষ!
                                        @elseif (session('lang_fin'))
                                            Loppuunmyyty!
                                        @else
                                            Out of Stock!
                                        @endif
                                    </div>
                                @endif

                                {{-- Wishlist Button --}}
                                <form action="" method="POST">
                                    @csrf
                                    <input type="hidden" name="product_id" value="{{$product->id}}">

                                    @if (App\Models\WishTable::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $product->id)->count() >= 1)
                                        <a class="wishlist-btn" href="{{route('wishlist.remove.btn', $product->id)}}">
                                            <i class="fas fa-heart clicked"></i>
                                        </a>
                                    @else
                                        <button class="wishlist-btn" formaction="{{route('wishlist.store')}}">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    @endif
                                </form>

                                {{-- Product Image --}}
                                <div class="product-thumb">
                                    <a href="{{route('product.details', $product->slug)}}">
                                        <img src="{{asset('uploads/product/preview')}}/{{$product->preview}}" alt="{{$product->product_name}}">
                                    </a>
                                </div>

                                {{-- Product Info --}}
                                <div class="product-info">
                                    <div class="product-category">
                                        @if (session('lang_fin'))
                                            {{ $product->relto_cata->cata_name_fin ?? $product->relto_cata->cata_name }}
                                        @elseif (session('lang_ben'))
                                            {{ $product->relto_cata->cata_name_ben ?? $product->relto_cata->cata_name }}
                                        @else
                                            {{ $product->relto_cata->cata_name }}
                                        @endif
                                    </div>

                                    <h3 class="product-title">
                                        <a href="{{route('product.details', $product->slug)}}">{{$product->product_name}}</a>
                                    </h3>

                                    <div class="product-rating">
                                        @php
                                            $avg_star = App\Models\OrdereditemsTab::where('product_id', $product->id)->avg('star');
                                        @endphp

                                        @for ($i = 1; $i <= $avg_star; $i++)
                                            <i class="fas fa-star filled"></i>

                                            @if ($avg_star - $i < 1 && $avg_star - $i > 0)
                                                <i class="fad fa-star-half" style="--fa-secondary-opacity: 1.0; --fa-primary-color: #FF9800; --fa-secondary-color: #D6DDE6;"></i>
                                            @endif
                                        @endfor

                                        @for ($i = 1; $i <= 5-$avg_star; $i++)
                                            <i class="fas fa-star"></i>
                                        @endfor

                                        <span class="small">({{App\Models\OrdereditemsTab::where('product_id', $product->id)->whereNotNull('review')->count()}})</span>
                                    </div>

                                    <div class="product-price">
                                        @if ($product->discount != 0)
                                            <span class="old-price">{{$product->price}}&#8364;</span>
                                            <span>{{number_format($product->after_disc)}}&#8364;</span>
                                        @else
                                            <span>{{number_format($product->price)}}&#8364;</span>
                                        @endif
                                    </div>

                                    {{-- Quick Add to Cart Section --}}
                                    <div class="quick-add-section mt-3">
                                        @php
                                            $total_qty = 0;
                                            $available_colors = App\Models\Inventory::where('product_id', $product->id)
                                                ->where('quantity', '>', 0)
                                                ->groupBy('color')
                                                ->pluck('color');
                                            $available_sizes = App\Models\Inventory::where('product_id', $product->id)
                                                ->where('quantity', '>', 0)
                                                ->groupBy('size')
                                                ->pluck('size');
                                        @endphp

                                        @foreach (App\Models\Inventory::where('product_id', $product->id)->get() as $inv_upd)
                                            @php
                                                $total_qty += $inv_upd->quantity;
                                            @endphp
                                        @endforeach

                                        @if($total_qty > 0)
                                            {{-- Quick Add Form --}}
                                            <form class="quick-add-form" data-product-id="{{$product->id}}">
                                                @csrf
                                                <input type="hidden" name="product_id" value="{{$product->id}}">

                                                {{-- Color Selection --}}
                                                @if($available_colors->count() > 0)
                                                    <div class="quick-select-group mb-2">
                                                        <select name="prod_color" class="form-control form-control-sm quick-color-select" required>
                                                            <option value="">Select Color</option>
                                                            @foreach($available_colors as $color_id)
                                                                @php
                                                                    $color = App\Models\Color::find($color_id);
                                                                @endphp
                                                                @if($color)
                                                                    <option value="{{$color->id}}">{{$color->color_name}}</option>
                                                                @endif
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                @endif

                                                {{-- Size Selection --}}
                                                @if($available_sizes->count() > 0)
                                                    <div class="quick-select-group mb-2">
                                                        <select name="prod_size" class="form-control form-control-sm quick-size-select" required>
                                                            <option value="">Select Size</option>
                                                            @foreach($available_sizes as $size_id)
                                                                @php
                                                                    $size = App\Models\Size::find($size_id);
                                                                @endphp
                                                                @if($size)
                                                                    <option value="{{$size->id}}">{{$size->size}}</option>
                                                                @endif
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                @endif

                                                {{-- Quantity and Add to Cart --}}
                                                <div class="d-flex align-items-center">
                                                    <div class="quantity-selector mr-2">
                                                        <select name="quantity" class="form-control form-control-sm quick-quantity-select" required>
                                                            <option value="1">1</option>
                                                        </select>
                                                    </div>
                                                    <button type="submit" class="btn btn-sm btn-primary quick-add-btn flex-grow-1">
                                                        <i class="fas fa-shopping-cart mr-1"></i>
                                                        @if (session('lang_ben'))
                                                            কার্টে যোগ করুন
                                                        @elseif (session('lang_fin'))
                                                            Lisää koriin
                                                        @else
                                                            Add to Cart
                                                        @endif
                                                    </button>
                                                </div>
                                            </form>
                                        @else
                                            <button class="btn btn-sm btn-secondary w-100" disabled>
                                                @if (session('lang_ben'))
                                                    স্টক শেষ
                                                @elseif (session('lang_fin'))
                                                    Loppuunmyyty
                                                @else
                                                    Out of Stock
                                                @endif
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12">
                            <div class="no-products">
                                <img src="{{asset('assets/img/no_product_found.gif')}}" alt="No Product Found">
                                <h4>
                                    @if (session('lang_ben'))
                                        ওহো! আইটেম পাওয়া যায়নি!
                                    @elseif (session('lang_fin'))
                                        Hups! Tuotetta ei löytynyt!
                                    @else
                                        Oops! Item not found!
                                    @endif
                                </h4>
                            </div>
                        </div>
                    @endforelse
                </div>

                <div class="shop-pagination">
                    {{$store_items->withQueryString()->links()}}
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ======================= All Product List ======================== -->
@endsection



@section('footer_script')

{{-- === Master Search === --}}
<script>
    $('.cate_box').change(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val();
        var subcate_id = '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = '';
        var size_id = '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    $('.subcate_box').click(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('input[name="subcate"]:checked').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = $('input[name="color"]:checked').val() || '';
        var size_id = $('input[name="size"]:checked').val() || '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    $('.brand_box').click(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('input[name="subcate"]:checked').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = $('input[name="color"]:checked').val() || '';
        var size_id = $('input[name="size"]:checked').val() || '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    $('.color_box').click(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('input[name="subcate"]:checked').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = $('input[name="color"]:checked').val() || '';
        var size_id = '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    $('.size_box').click(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('input[name="subcate"]:checked').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = '';
        var size_id = $('input[name="size"]:checked').val() || '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    $('.show_box').change(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('input[name="subcate"]:checked').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = $('input[name="color"]:checked').val() || '';
        var size_id = $('input[name="size"]:checked').val() || '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    $('.sort_box').change(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('input[name="subcate"]:checked').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = $('input[name="color"]:checked').val() || '';
        var size_id = $('input[name="size"]:checked').val() || '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    $('.price_box').click(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('input[name="subcate"]:checked').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = $('input[name="color"]:checked').val() || '';
        var size_id = $('input[name="size"]:checked').val() || '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });

    // Handle subcategory dropdown when no category is selected
    $('.subcate_dropdown').change(function(){
        var master_inp = $('#master_inp').length ? $('#master_inp').val() : '';
        var cate_id = $('.cate_box').val() || '';
        var subcate_id = $('.subcate_dropdown').val() || '';
        var brand_id = $('input[name="brands"]:checked').val() || '';
        var min_price = $('.min_price').val() || '';
        var max_price = $('.max_price').val() || '';
        var color_id = $('input[name="color"]:checked').val() || '';
        var size_id = $('input[name="size"]:checked').val() || '';
        var sort = $('.sort_box').val() || '';
        var show = $('.show_box').val() || '';

        var search_link = "{{route('shop_page')}}" + "?inp=" + encodeURIComponent(master_inp) + "&cate=" + encodeURIComponent(cate_id) + "&subcate=" + encodeURIComponent(subcate_id) + "&brand=" + encodeURIComponent(brand_id) + "&min=" + encodeURIComponent(min_price) + "&max=" + encodeURIComponent(max_price) + "&col=" + encodeURIComponent(color_id) + "&siz=" + encodeURIComponent(size_id) + "&sort=" + encodeURIComponent(sort) + "&show=" + encodeURIComponent(show) + "&chk=" + 'qry';
        window.location.href = search_link;
    });
</script>

{{-- === Quick Add to Cart Functionality === --}}
<script>
// Simple notification function
function showNotification(message, type) {
    var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show notification-popup" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
        '<i class="fas ' + iconClass + ' mr-2"></i>' + message +
        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
        '<span aria-hidden="true">&times;</span>' +
        '</button>' +
        '</div>');

    $('body').append(notification);

    // Auto remove after 5 seconds
    setTimeout(function() {
        notification.alert('close');
    }, 5000);
}

$(document).ready(function() {
    // Handle color change to update available sizes
    $('.quick-color-select').change(function() {
        var productId = $(this).closest('.quick-add-form').data('product-id');
        var colorId = $(this).val();
        var sizeSelect = $(this).closest('.quick-add-form').find('.quick-size-select');
        var quantitySelect = $(this).closest('.quick-add-form').find('.quick-quantity-select');

        if (colorId) {
            // Get available sizes for this color
            $.post('/get_size', {
                _token: '{{ csrf_token() }}',
                product_id: productId,
                color_id: colorId
            }, function(data) {
                sizeSelect.html('<option value="">Select Size</option>');
                if (data.trim()) {
                    // Parse the returned HTML and extract options
                    var tempDiv = $('<div>').html(data);
                    tempDiv.find('input[type="radio"]').each(function() {
                        var value = $(this).val();
                        var label = $(this).next('label').text();
                        sizeSelect.append('<option value="' + value + '">' + label + '</option>');
                    });
                }
                // Reset quantity
                quantitySelect.html('<option value="1">1</option>');
            });
        } else {
            sizeSelect.html('<option value="">Select Size</option>');
            quantitySelect.html('<option value="1">1</option>');
        }
    });

    // Handle size change to update available quantities
    $('.quick-size-select').change(function() {
        var productId = $(this).closest('.quick-add-form').data('product-id');
        var colorId = $(this).closest('.quick-add-form').find('.quick-color-select').val();
        var sizeId = $(this).val();
        var quantitySelect = $(this).closest('.quick-add-form').find('.quick-quantity-select');

        if (colorId && sizeId) {
            $.post('/get_quantity', {
                _token: '{{ csrf_token() }}',
                product_id: productId,
                color_id: colorId,
                size_id: sizeId
            }, function(data) {
                quantitySelect.html(data);
            });
        } else {
            quantitySelect.html('<option value="1">1</option>');
        }
    });

    // Handle quick add to cart form submission
    $('.quick-add-form').submit(function(e) {
        e.preventDefault();

        var form = $(this);
        var button = form.find('.quick-add-btn');
        var originalText = button.html();

        // Validate required fields
        var colorSelect = form.find('.quick-color-select');
        var sizeSelect = form.find('.quick-size-select');

        if (colorSelect.length && !colorSelect.val()) {
            alert('Please select a color');
            return;
        }

        if (sizeSelect.length && !sizeSelect.val()) {
            alert('Please select a size');
            return;
        }

        // Show loading state
        button.addClass('loading').prop('disabled', true);

        // Submit form data
        console.log('Submitting cart form:', form.serialize());
        $.ajax({
            url: '{{ route("cart.store") }}',
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                console.log('Cart response:', response);
                if (response.success) {
                    // Show success state
                    button.removeClass('loading').addClass('success');
                    button.html('<i class="fas fa-check mr-1"></i> Added!');

                    // Reset after 2 seconds
                    setTimeout(function() {
                        button.removeClass('success').prop('disabled', false);
                        button.html(originalText);
                    }, 2000);

                    // Show success message
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message || 'Item added to cart successfully!');
                    } else {
                        // Create a simple success notification
                        showNotification(response.message || 'Item added to cart successfully!', 'success');
                    }

                    // Update cart count if element exists
                    if ($('.cart-count').length) {
                        $('.cart-count').text(response.cart_count || 0);
                    }

                    // Update all cart count elements
                    $('.dn-counter').text(response.cart_count || 0);

                    // Reload page after 2 seconds to refresh cart sidebar
                    setTimeout(function() {
                        location.reload();
                    }, 2500);
                } else {
                    button.removeClass('loading').prop('disabled', false);
                    if (typeof toastr !== 'undefined') {
                        toastr.error(response.message || 'Error adding item to cart');
                    } else {
                        showNotification(response.message || 'Error adding item to cart', 'error');
                    }
                }
            },
            error: function(xhr) {
                console.log('Cart error:', xhr);
                button.removeClass('loading').prop('disabled', false);

                if (xhr.status === 401) {
                    var response = xhr.responseJSON;
                    if (response && response.redirect) {
                        if (confirm('Please login to add items to cart. Redirect to login page?')) {
                            window.location.href = response.redirect;
                        }
                    } else {
                        showNotification('Please login to add items to cart', 'error');
                    }
                } else {
                    var errorMessage = 'Error adding item to cart';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        // Handle validation errors
                        var errors = xhr.responseJSON.errors;
                        errorMessage = Object.values(errors).flat().join(', ');
                    }

                    if (typeof toastr !== 'undefined') {
                        toastr.error(errorMessage);
                    } else {
                        showNotification(errorMessage, 'error');
                    }
                }
            }
        });
    });
});
</script>

{{-- === Scroll while Query string === --}}
<script>
    $(document).ready(function () {
        if ($('#crump').length) {
            $('html, body').animate({
                scrollTop: $('#crump').offset().top
            }, 'fast');
        }
    });
</script>
@endsection