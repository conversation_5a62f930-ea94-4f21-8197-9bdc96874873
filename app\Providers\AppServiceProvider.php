<?php

namespace App\Providers;

use App\Models\SiteinfoTab;
use App\View\Composers\CartComposer;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Paginator::useBootstrap();

        // Share the site settings and categories with all views
        View::composer('*', function ($view) {
            $setting = SiteinfoTab::find(1);
            $categories = \App\Models\category::orderBy('cata_name')->get();
            $view->with([
                'setting' => $setting,
                'categories' => $categories
            ]);
        });

        // Register cart composer for master layout
        View::composer('layouts.master', CartComposer::class);
    }
}
